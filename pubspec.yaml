name: darvis_app
description: "<PERSON><PERSON> AI Assistant - The Conversational AI Second Brain"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'
  flutter: ">=3.13.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.3
  bloc: ^8.1.2
  equatable: ^2.0.5

  # UI & Widgets
  flutter_svg: ^2.0.7

  # API Communication
  dio: ^5.3.2
  dio_smart_retry: ^7.0.1


  # Real-time Voice
  livekit_client: ^2.4.8

  # Authentication & Firebase
  firebase_core: ^2.17.0
  firebase_auth: ^4.10.1
  firebase_messaging: ^14.6.9
  firebase_crashlytics: ^3.4.8
  firebase_performance: ^0.9.3+8

  # Local Storage & Offline
  # isar: ^3.1.0+1  # Temporarily removed due to analyzer version conflict
  # isar_flutter_libs: ^3.1.0+1  # Will re-add when implementing Phase 1

  # Security
  flutter_secure_storage: ^9.0.0
  encrypt: ^5.0.1

  # Service Location
  get_it: ^7.6.4

  # Performance & Caching
  cached_network_image: ^3.3.0

  # UI & Icons
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Testing
  bloc_test: ^9.1.4
  mocktail: ^1.0.0
  golden_toolkit: ^0.15.0

  # Build Tools
  build_runner: ^2.4.7
  yaml: ^3.1.2  # For design token generation
  # isar_generator: ^3.1.0+1  # Temporarily removed due to analyzer version conflict

  # Linting
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/

  # Fonts
  fonts:
    - family: Outfit
      fonts:
        - asset: assets/fonts/Outfit-Regular.ttf
        - asset: assets/fonts/Outfit-SemiBold.ttf
          weight: 600
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
