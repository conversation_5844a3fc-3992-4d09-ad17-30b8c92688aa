import 'dart:math';

/// Factory for generating realistic mock data for testing
class MockDataFactory {
  static final Random _random = Random();

  // Sample data pools
  static const List<String> _firstNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  ];

  static const List<String> _lastNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  ];

  static const List<String> _taskTitles = [
    'Review project proposal',
    'Schedule team meeting',
    'Update documentation',
    'Fix critical bug',
    'Implement new feature',
    'Conduct user research',
    'Prepare presentation',
    'Analyze performance metrics',
    'Refactor legacy code',
    'Set up CI/CD pipeline',
  ];

  static const List<String> _taskDescriptions = [
    'This task requires careful attention to detail and coordination with multiple team members.',
    'A straightforward task that should be completed by end of week.',
    'High priority item that needs immediate attention.',
    'This will help improve overall system performance.',
    'User-facing feature that will enhance the experience.',
    'Research task to gather insights for future development.',
    'Preparation work for upcoming stakeholder meeting.',
    'Analysis task to identify optimization opportunities.',
    'Technical debt reduction to improve maintainability.',
    'Infrastructure improvement for better deployment process.',
  ];

  static const List<String> _noteTitles = [
    'Meeting Notes - Project Kickoff',
    'Research Findings',
    'Ideas for Q4 Planning',
    'Technical Architecture Notes',
    'User Feedback Summary',
    'Performance Optimization Ideas',
    'Security Considerations',
    'Design System Guidelines',
    'API Documentation Notes',
    'Testing Strategy',
  ];

  static const List<String> _chatMessages = [
    'Hello! How can I help you today?',
    'Can you help me create a new task?',
    'What\'s the weather like today?',
    'I need to schedule a meeting for next week.',
    'Can you summarize my notes from yesterday?',
    'Help me prioritize my tasks.',
    'What are some productivity tips?',
    'I\'m feeling overwhelmed with work.',
    'Can you remind me about my upcoming deadlines?',
    'Let\'s brainstorm some ideas for the project.',
  ];

  /// Generate a random user
  static Map<String, dynamic> createUser({
    String? id,
    String? email,
    String? name,
  }) {
    final firstName = _firstNames[_random.nextInt(_firstNames.length)];
    final lastName = _lastNames[_random.nextInt(_lastNames.length)];
    final generatedName = name ?? '$firstName $lastName';
    final generatedEmail = email ?? '${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com';

    return {
      'id': id ?? 'user_${_generateId()}',
      'email': generatedEmail,
      'name': generatedName,
      'created_at': _randomPastDate().toIso8601String(),
      'updated_at': _randomRecentDate().toIso8601String(),
    };
  }

  /// Generate a random task
  static Map<String, dynamic> createTask({
    String? id,
    String? title,
    String? description,
    bool? completed,
  }) {
    final createdAt = _randomPastDate();
    final updatedAt = _randomDateAfter(createdAt);

    return {
      'id': id ?? 'task_${_generateId()}',
      'title': title ?? _taskTitles[_random.nextInt(_taskTitles.length)],
      'description': description ?? _taskDescriptions[_random.nextInt(_taskDescriptions.length)],
      'completed': completed ?? _random.nextBool(),
      'priority': _random.nextInt(3) + 1, // 1-3
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Generate a random note
  static Map<String, dynamic> createNote({
    String? id,
    String? title,
    String? content,
  }) {
    final createdAt = _randomPastDate();
    final updatedAt = _randomDateAfter(createdAt);
    final noteTitle = title ?? _noteTitles[_random.nextInt(_noteTitles.length)];
    final noteContent = content ?? _generateNoteContent();

    return {
      'id': id ?? 'note_${_generateId()}',
      'title': noteTitle,
      'content': noteContent,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Generate a random chat message
  static Map<String, dynamic> createChatMessage({
    String? id,
    String? content,
    String? role,
    String? conversationId,
  }) {
    return {
      'id': id ?? 'msg_${_generateId()}',
      'content': content ?? _chatMessages[_random.nextInt(_chatMessages.length)],
      'role': role ?? (_random.nextBool() ? 'user' : 'assistant'),
      'conversation_id': conversationId ?? 'conv_${_generateId()}',
      'timestamp': _randomRecentDate().toIso8601String(),
    };
  }

  /// Generate a random conversation
  static Map<String, dynamic> createConversation({
    String? id,
    String? title,
    List<Map<String, dynamic>>? messages,
  }) {
    final createdAt = _randomPastDate();
    final messageCount = _random.nextInt(10) + 1;
    final conversationId = id ?? 'conv_${_generateId()}';
    
    final generatedMessages = messages ?? List.generate(messageCount, (index) {
      return createChatMessage(
        conversationId: conversationId,
        role: index % 2 == 0 ? 'user' : 'assistant',
      );
    });

    return {
      'id': conversationId,
      'title': title ?? 'Conversation ${_generateId()}',
      'messages': generatedMessages,
      'created_at': createdAt.toIso8601String(),
      'updated_at': _randomDateAfter(createdAt).toIso8601String(),
    };
  }

  /// Generate multiple tasks
  static List<Map<String, dynamic>> createTasks(int count) {
    return List.generate(count, (_) => createTask());
  }

  /// Generate multiple notes
  static List<Map<String, dynamic>> createNotes(int count) {
    return List.generate(count, (_) => createNote());
  }

  /// Generate multiple conversations
  static List<Map<String, dynamic>> createConversations(int count) {
    return List.generate(count, (_) => createConversation());
  }

  // Helper methods

  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() + 
           _random.nextInt(1000).toString().padLeft(3, '0');
  }

  static DateTime _randomPastDate() {
    final daysAgo = _random.nextInt(30) + 1;
    return DateTime.now().subtract(Duration(days: daysAgo));
  }

  static DateTime _randomRecentDate() {
    final hoursAgo = _random.nextInt(24);
    return DateTime.now().subtract(Duration(hours: hoursAgo));
  }

  static DateTime _randomDateAfter(DateTime date) {
    final hoursAfter = _random.nextInt(48);
    return date.add(Duration(hours: hoursAfter));
  }

  static String _generateNoteContent() {
    final sentences = [
      'This is an important note about the project.',
      'Key insights from today\'s research session.',
      'Action items that need to be completed.',
      'Ideas for improving the user experience.',
      'Technical considerations for the implementation.',
      'Feedback from stakeholders and team members.',
      'Next steps and timeline for delivery.',
    ];

    final sentenceCount = _random.nextInt(4) + 2;
    final selectedSentences = <String>[];
    
    for (int i = 0; i < sentenceCount; i++) {
      selectedSentences.add(sentences[_random.nextInt(sentences.length)]);
    }

    return selectedSentences.join('\n\n');
  }
}
