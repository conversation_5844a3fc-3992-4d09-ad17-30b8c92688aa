Launching lib\main.dart on 21081111RG in debug mode...
√ Built build\app\outputs\flutter-apk\app-debug.apk
I/flutter ( 3460): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
W/FlutterWebRTCPlugin( 3460): audioFocusChangeListener [Earpiece(name=Earpiece)] Earpiece(name=Earpiece)
W/FlutterWebRTCPlugin( 3460): audioFocusChangeListener [Speakerphone(name=Speakerphone), Earpiece(name=Earpiece)] Speakerphone(name=Speakerphone)
Connecting to VM Service at ws://127.0.0.1:12487/2wrtasuH5d4=/ws
Connected to the VM Service.
I/Choreographer( 3460): Skipped 75 frames!  The application may be doing too much work on its main thread.
W/Looper  ( 3460): PerfMonitor doFrame : time=1ms vsyncFrame=0 latency=628ms procState=-1 historyMsgCount=1
W/libc    ( 3460): Access denied finding property "persist.vendor.debug.gpud.enable"
E/LB      ( 3460): fail to open file: No such file or directory
E/LB      ( 3460): fail to open node: No such file or directory
W/libc    ( 3460): Access denied finding property "ro.vendor.display.iris_x7.support"
D/BLASTBufferQueue( 3460): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:0,a:1) acquireNextBufferLocked size=1080x2270 mFrameNumber=1 applyTransaction=true mTimestamp=230127746092485(auto) mPendingTransactions.size=0 graphicBufferId=14860586844174 transform=0
W/FinalizerDaemon( 3460): type=1400 audit(0.0:131787): avc:  denied  { getopt } for  path="/dev/socket/usap_pool_primary" scontext=u:r:untrusted_app:s0:c173,c256,c512,c768 tcontext=u:r:zygote:s0 tclass=unix_stream_socket permissive=0 app=com.example.darvis_app


2ND ATTEMPT
E/flutter (32460): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: Unable to load asset: "assets/icons/home_icon.svg".
E/flutter (32460): The asset does not exist or has empty data.
E/flutter (32460): #0      PlatformAssetBundle.load.<anonymous closure> (package:flutter/src/services/asset_bundle.dart:333:13)
asset_bundle.dart:333
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #1      SvgLoader._load.<anonymous closure> (package:flutter_svg/src/loaders.dart:153:41)
loaders.dart:153
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #2      _VectorGraphicWidgetState._loadPicture.<anonymous closure> (package:vector_graphics/src/vector_graphics.dart:362:40)
vector_graphics.dart:362
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #3      _VectorGraphicWidgetState._loadPicture.<anonymous closure> (package:vector_graphics/src/vector_graphics.dart:370:13)
vector_graphics.dart:370
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #4      _VectorGraphicWidgetState._loadAssetBytes (package:vector_graphics/src/vector_graphics.dart:409:33)
vector_graphics.dart:409
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): 
E/flutter (32460): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: Unable to load asset: "assets/icons/home_icon.svg".
E/flutter (32460): The asset does not exist or has empty data.
E/flutter (32460): #0      PlatformAssetBundle.load.<anonymous closure> (package:flutter/src/services/asset_bundle.dart:333:13)
asset_bundle.dart:333
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #1      SvgLoader._load.<anonymous closure> (package:flutter_svg/src/loaders.dart:153:41)
loaders.dart:153
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #2      _VectorGraphicWidgetState._loadPicture.<anonymous closure> (package:vector_graphics/src/vector_graphics.dart:362:40)
vector_graphics.dart:362
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #3      _VectorGraphicWidgetState._loadPicture.<anonymous closure> (package:vector_graphics/src/vector_graphics.dart:370:13)
vector_graphics.dart:370
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #4      _VectorGraphicWidgetState._loadAssetBytes (package:vector_graphics/src/vector_graphics.dart:409:33)
vector_graphics.dart:409
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): 
E/flutter (32460): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: Unable to load asset: "assets/icons/darvis_navbar_icon.svg".
E/flutter (32460): The asset does not exist or has empty data.
E/flutter (32460): #0      PlatformAssetBundle.load.<anonymous closure> (package:flutter/src/services/asset_bundle.dart:333:13)
asset_bundle.dart:333
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #1      SvgLoader._load.<anonymous closure> (package:flutter_svg/src/loaders.dart:153:41)
loaders.dart:153
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #2      _VectorGraphicWidgetState._loadPicture.<anonymous closure> (package:vector_graphics/src/vector_graphics.dart:362:40)
vector_graphics.dart:362
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #3      _VectorGraphicWidgetState._loadPicture.<anonymous closure> (package:vector_graphics/src/vector_graphics.dart:370:13)
vector_graphics.dart:370
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #4      _VectorGraphicWidgetState._loadAssetBytes (package:vector_graphics/src/vector_graphics.dart:409:33)
vector_graphics.dart:409
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): 
E/flutter (32460): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: Unable to load asset: "assets/icons/darvis_navbar_icon.svg".
E/flutter (32460): The asset does not exist or has empty data.
E/flutter (32460): #0      PlatformAssetBundle.load.<anonymous closure> (package:flutter/src/services/asset_bundle.dart:333:13)
asset_bundle.dart:333
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #1      SvgLoader._load.<anonymous closure> (package:flutter_svg/src/loaders.dart:153:41)
loaders.dart:153
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #2      _VectorGraphicWidgetState._loadPicture.<anonymous closure> (package:vector_graphics/src/vector_graphics.dart:362:40)
vector_graphics.dart:362
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #3      _VectorGraphicWidgetState._loadPicture.<anonymous closure> (package:vector_graphics/src/vector_graphics.dart:370:13)
vector_graphics.dart:370
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): #4      _VectorGraphicWidgetState._loadAssetBytes (package:vector_graphics/src/vector_graphics.dart:409:33)
vector_graphics.dart:409
E/flutter (32460): <asynchronous suspension>
E/flutter (32460): 
Restarted application in 5,594ms.



