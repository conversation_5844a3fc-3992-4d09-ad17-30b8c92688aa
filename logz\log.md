
════════ Exception caught by image resource service ════════════════════════════
The following assertion was thrown resolving an image codec:
Unable to load asset: "assets/images/darvisintelligence.PNG".
Exception: Asset not found

When the exception was thrown, this was the stack:
#0      PlatformAssetBundle.loadBuffer (package:flutter/src/services/asset_bundle.dart:374:7)
asset_bundle.dart:374
<asynchronous suspension>
#1      AssetBundleImageProvider._loadAsync (package:flutter/src/painting/image_provider.dart:787:16)
image_provider.dart:787
<asynchronous suspension>
#2      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1027:3)
image_stream.dart:1027
<asynchronous suspension>

Image provider: AssetImage(bundle: null, name: "assets/images/darvisintelligence.PNG")
Image key: AssetBundleImageKey(bundle: PlatformAssetBundle#dcc28(), name: "assets/images/darvisintelligence.PNG", scale: 1.0)
════════════════════════════════════════════════════════════════════════════════
I/Choreographer(16664): Skipped 8750 frames!  The application may be doing too much work on its main thread.
D/VRI[MainActivity](16664): vri.Setup new sync=wmsSync-VRI[MainActivity]#2
D/OpenGLRenderer(16664): makeCurrent grContext:0xb400006f9ce825c0 reset mTextureAvailable
D/mple.darvis_app(16664): MiuiProcessManagerServiceStub setSchedFifo
I/MiuiProcessManagerImpl(16664): setSchedFifo pid:16664, mode:3
W/libc    (16664): Access denied finding property "ro.vendor.display.iris_x7.support"

════════ Exception caught by rendering library ═════════════════════════════════
A RenderFlex overflowed by 89 pixels on the right.
The relevant error-causing widget was:
    Row Row:file:///C:/Users/<USER>/Documents/DAVID.DEV/darvis-app/lib/screens/home/<USER>
════════════════════════════════════════════════════════════════════════════════
Restarted application in 5,436ms.
D/BLASTBufferQueue(16664): [VRI[MainActivity]#0](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=232217709745840(auto) mPendingTransactions.size=0 graphicBufferId=71571335020563 transform=0
D/VRI[MainActivity](16664): vri.reportDrawFinished
W/Looper  (16664): PerfMonitor doFrame : time=276ms vsyncFrame=0 latency=72497ms procState=-1 historyMsgCount=6
I/Choreographer(16664): Skipped 33 frames!  The application may be doing too much work on its main thread.
I/OpenGLRenderer(16664): Davey! duration=72770ms; Flags=1, FrameTimelineVsyncId=84089911, IntendedVsync=232144941588929, Vsync=232217430780179, InputEventId=0, HandleInputStart=232217439946071, AnimationStart=232217439952225, PerformTraversalsStart=232217439956609, DrawStart=232217444800532, FrameDeadline=232144951588929, FrameInterval=232217439000302, FrameStartTime=8284479, SyncQueued=232217451147071, SyncStart=232217451701148, IssueDrawCommandsStart=232217462987379, SwapBuffers=232217706280917, FrameCompleted=232217712785148, DequeueBufferDuration=5616923, QueueBufferDuration=2720615, GpuCompleted=232217709875994, SwapBuffersCompleted=232217712785148, DisplayPresentTime=-5476376667473384088, CommandSubmissionCompleted=232217706280917, 
I/HandWritingStubImpl(16664): refreshLastKeyboardType: 1
2
I/HandWritingStubImpl(16664): getCurrentKeyboardType: 1
I/ScrollIdentify(16664): on fling
I/mple.darvis_app(16664): Thread[2,tid=24819,WaitingInMainSignalCatcherLoop,Thread*=0xb400006fa3c00000,peer=0x15080298,"Signal Catcher"]: reacting to signal 3
I/mple.darvis_app(16664): 
W/MIUIScout ANR(16664): AnrScout only want msg within 20s, so stop here
D/MIUIScout ANR(16664): get period history msg: (Current message:null)
D/MIUIScout ANR(16664): get period history msg:messages for the same target android.view.Choreographer$FrameHandler execute time>50ms (count=2 totalwalltime=277ms h=android.view.Choreographer$FrameHandler whatORcallback=android.view.Choreographer$FrameDisplayEventReceiver)
D/MIUIScout ANR(16664): get period history msg:execute time>50ms (msgIndex=6seq=730 plan=06:22:54.917 late=72497ms wall=276ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
D/MIUIScout ANR(16664): get period history msg:In recent 20s, total historyMsgCount=22
E/MQSEventManagerDelegate(16664): failed to get MQSService.
W/MIUIScout ANR(16664): Print Message Id: 0 MessageInfo: { when=-9s525ms callback=io.flutter.embedding.engine.dart.DartMessenger$$ExternalSyntheticLambda0 target=android.os.Handler }
W/MIUIScout ANR(16664): Print Message Id: 1 MessageInfo: { when=-9s167ms callback=io.flutter.embedding.engine.dart.DartMessenger$$ExternalSyntheticLambda0 target=android.os.Handler }
W/MIUIScout ANR(16664): Print Message Id: 2 MessageInfo: { when=-8s771ms callback=io.flutter.embedding.engine.dart.DartMessenger$$ExternalSyntheticLambda0 target=android.os.Handler }
W/MIUIScout ANR(16664): (Print messages: 3, polling=false, quitting=false)
I/mple.darvis_app(16664): Wrote stack traces to tombstoned