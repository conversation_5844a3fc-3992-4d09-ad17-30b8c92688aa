import 'dart:async';
import 'dart:collection';

import 'api_service.dart';

/// Offline-first sync engine for managing data synchronization
class SyncEngine {
  final ApiService _apiService;
  
  final Queue<SyncOperation> _syncQueue = Queue<SyncOperation>();
  final StreamController<SyncStatus> _syncStatusController = StreamController<SyncStatus>.broadcast();
  
  Timer? _syncTimer;
  bool _isSyncing = false;

  SyncEngine({required ApiService apiService}) : _apiService = apiService;

  /// Stream of sync status updates
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Current sync status
  SyncStatus get currentStatus => _syncStatusController.hasListener 
      ? SyncStatus.synced 
      : SyncStatus.offline;

  /// Initialize the sync engine
  Future<void> initialize() async {
    // TODO: Initialize Isar database
    // _isar = await Isar.open([/* schemas */]);
    
    // Start periodic sync
    _startPeriodicSync();
  }

  /// Add operation to sync queue
  void queueOperation(SyncOperation operation) {
    _syncQueue.add(operation);
    _triggerSync();
  }

  /// Queue a create operation
  void queueCreate<T>({
    required String entityType,
    required Map<String, dynamic> data,
    required String localId,
  }) {
    queueOperation(SyncOperation(
      type: SyncOperationType.create,
      entityType: entityType,
      data: data,
      localId: localId,
      timestamp: DateTime.now(),
    ));
  }

  /// Queue an update operation
  void queueUpdate<T>({
    required String entityType,
    required String id,
    required Map<String, dynamic> data,
  }) {
    queueOperation(SyncOperation(
      type: SyncOperationType.update,
      entityType: entityType,
      entityId: id,
      data: data,
      timestamp: DateTime.now(),
    ));
  }

  /// Queue a delete operation
  void queueDelete<T>({
    required String entityType,
    required String id,
  }) {
    queueOperation(SyncOperation(
      type: SyncOperationType.delete,
      entityType: entityType,
      entityId: id,
      data: {}, // Empty data for delete operations
      timestamp: DateTime.now(),
    ));
  }

  /// Trigger immediate sync
  Future<void> sync() async {
    if (_isSyncing) return;
    
    _isSyncing = true;
    _syncStatusController.add(SyncStatus.syncing);

    try {
      while (_syncQueue.isNotEmpty) {
        final operation = _syncQueue.removeFirst();
        await _processOperation(operation);
      }
      
      _syncStatusController.add(SyncStatus.synced);
    } catch (e) {
      _syncStatusController.add(SyncStatus.error);
      print('Sync error: $e');
    } finally {
      _isSyncing = false;
    }
  }

  /// Process a single sync operation
  Future<void> _processOperation(SyncOperation operation) async {
    try {
      switch (operation.type) {
        case SyncOperationType.create:
          await _processCreateOperation(operation);
          break;
        case SyncOperationType.update:
          await _processUpdateOperation(operation);
          break;
        case SyncOperationType.delete:
          await _processDeleteOperation(operation);
          break;
      }
    } catch (e) {
      // Re-queue operation for retry
      _syncQueue.add(operation.copyWith(retryCount: operation.retryCount + 1));
      
      // If max retries reached, mark as failed
      if (operation.retryCount >= 3) {
        print('Operation failed after max retries: ${operation.entityType}');
        // TODO: Store failed operations for manual resolution
      }
      
      rethrow;
    }
  }

  /// Process create operation
  Future<void> _processCreateOperation(SyncOperation operation) async {
    switch (operation.entityType) {
      case 'task':
        final response = await _apiService.createTask(
          title: operation.data['title'],
          description: operation.data['description'],
        );
        // TODO: Update local entity with server ID
        break;
      case 'note':
        final response = await _apiService.createNote(
          title: operation.data['title'],
          content: operation.data['content'],
        );
        // TODO: Update local entity with server ID
        break;
      default:
        throw Exception('Unknown entity type: ${operation.entityType}');
    }
  }

  /// Process update operation
  Future<void> _processUpdateOperation(SyncOperation operation) async {
    switch (operation.entityType) {
      case 'task':
        await _apiService.updateTask(
          taskId: operation.entityId!,
          updates: operation.data,
        );
        break;
      default:
        throw Exception('Unknown entity type: ${operation.entityType}');
    }
  }

  /// Process delete operation
  Future<void> _processDeleteOperation(SyncOperation operation) async {
    // TODO: Implement delete operations when API supports them
    print('Delete operation not yet implemented for ${operation.entityType}');
  }

  /// Start periodic sync timer
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _triggerSync();
    });
  }

  /// Trigger sync if not already syncing
  void _triggerSync() {
    if (!_isSyncing && _syncQueue.isNotEmpty) {
      sync();
    }
  }

  /// Dispose of resources
  void dispose() {
    _syncTimer?.cancel();
    _syncStatusController.close();
    // TODO: Close database when implemented
  }
}

/// Sync operation model
class SyncOperation {
  final SyncOperationType type;
  final String entityType;
  final String? entityId;
  final String? localId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int retryCount;

  SyncOperation({
    required this.type,
    required this.entityType,
    this.entityId,
    this.localId,
    required this.data,
    required this.timestamp,
    this.retryCount = 0,
  });

  SyncOperation copyWith({
    SyncOperationType? type,
    String? entityType,
    String? entityId,
    String? localId,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    int? retryCount,
  }) {
    return SyncOperation(
      type: type ?? this.type,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      localId: localId ?? this.localId,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      retryCount: retryCount ?? this.retryCount,
    );
  }
}

/// Types of sync operations
enum SyncOperationType { create, update, delete }

/// Sync status states
enum SyncStatus { synced, syncing, offline, error }
