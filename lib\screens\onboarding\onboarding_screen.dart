// lib/screens/onboarding/onboarding_screen.dart

// Assets needed:
// assets/images/onboarding_brain.PNG
// assets/images/onboarding_cube.PNG
// assets/images/onboarding_orb.PNG

import 'dart:async';
import 'package:darvis_app/screens/auth/login_screen.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:flutter/material.dart';

// Naming reusable constants for asset paths, as requested.
const String _kBrainImage = 'assets/images/onboarding_brain.PNG';
const String _kCubeImage = 'assets/images/onboarding_cube.PNG';
const String _kOrbImage = 'assets/images/onboarding_orb.PNG';

// Local, temporary text styles until the generator issue is resolved.
// This is an acceptable workaround to keep moving.
const TextStyle _onboardingHeadlineStyle = TextStyle(
  fontFamily: DesignTokens.fontFamilyHeading,
  fontSize: 32,
  fontWeight: FontWeight.w700,
  color: DesignTokens.textPrimary,
);

const TextStyle _onboardingBodyStyle = TextStyle(
  fontFamily: DesignTokens.fontFamilyBody,
  fontSize: 16,
  fontWeight: FontWeight.w400,
  color: DesignTokens.textSecondary,
);

const TextStyle _onboardingButtonStyle = TextStyle(
  fontFamily: DesignTokens.fontFamilyBody,
  fontSize: 16,
  fontWeight: FontWeight.w400,
  color: DesignTokens.textPrimary,
);

const TextStyle _onboardingButtonBoldStyle = TextStyle(
  fontFamily: DesignTokens.fontFamilyBody,
  fontSize: 16,
  fontWeight: FontWeight.w700,
  color: DesignTokens.textPrimary,
);

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPageIndex = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentPageIndex = index;
    });
  }

  void _onOnboardingComplete() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (_) => const LoginScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: Stack(
        children: [
          PageView(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            children: const [
              _OnboardingPageContent(
                imagePath: _kBrainImage,
                headline: 'Meet Darvis.\nYour personal AI.',
                body:
                    'A proactive partner that learns with you, helping to organize your thoughts, manage tasks, and unlock your potential.',
              ),
              _OnboardingPageContent(
                imagePath: _kCubeImage,
                headline: 'A mode for every moment',
                body:
                    'From therapy and learning to event planning and productivity, Darvis adapts its personality and tools to fit your current goal.',
              ),
              _OnboardingPageContent(
                imagePath: _kOrbImage,
                headline: 'Capture everything.\nControl everything.',
                body:
                    'Save ideas, links, and notes with a single tap. You always decide what Darvis remembers, ensuring your space remains private and secure.',
              ),
            ],
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.only(bottom: DesignTokens.spacingXxl),
              child: _BottomNavigationBar(
                currentPageIndex: _currentPageIndex,
                pageCount: 3,
                onSkip: _onOnboardingComplete,
                onNext: () {
                  if (_currentPageIndex == 2) {
                    _onOnboardingComplete();
                  } else {
                    _pageController.nextPage(
                      duration: const Duration(milliseconds: 400),
                      curve: Curves.easeInOut,
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _OnboardingPageContent extends StatelessWidget {
  final String imagePath;
  final String headline;
  final String body;

  const _OnboardingPageContent({
    required this.imagePath,
    required this.headline,
    required this.body,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingXl),
      child: Column(
        children: [
          const Spacer(flex: 2),
          _AnimatedHoverImage(imagePath: imagePath),
          const Spacer(flex: 1),
          Text(
            headline,
            textAlign: TextAlign.center,
            style: _onboardingHeadlineStyle,
          ),
          const SizedBox(height: DesignTokens.spacingMd),
          Text(
            body,
            textAlign: TextAlign.center,
            style: _onboardingBodyStyle,
          ),
          const Spacer(flex: 3),
        ],
      ),
    );
  }
}

class _AnimatedHoverImage extends StatefulWidget {
  final String imagePath;

  const _AnimatedHoverImage({required this.imagePath});

  @override
  State<_AnimatedHoverImage> createState() => _AnimatedHoverImageState();
}

class _AnimatedHoverImageState extends State<_AnimatedHoverImage>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat(reverse: true);

    _animation = Tween<Offset>(
      begin: const Offset(0, -0.02),
      end: const Offset(0, 0.02),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: Image.asset(
        widget.imagePath,
        height: MediaQuery.of(context).size.height * 0.35,
        fit: BoxFit.contain,
      ),
    );
  }
}

class _BottomNavigationBar extends StatelessWidget {
  final int currentPageIndex;
  final int pageCount;
  final VoidCallback onSkip;
  final VoidCallback onNext;

  const _BottomNavigationBar({
    required this.currentPageIndex,
    required this.pageCount,
    required this.onSkip,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            onPressed: onSkip,
            child: const Text('Skip', style: _onboardingButtonStyle),
          ),
          _PageIndicator(
            pageCount: pageCount,
            currentPageIndex: currentPageIndex,
          ),
          TextButton(
            onPressed: onNext,
            child: Text(
              currentPageIndex == pageCount - 1 ? 'Get Started' : 'Next',
              style: currentPageIndex == pageCount - 1
                  ? _onboardingButtonBoldStyle
                  : _onboardingButtonStyle,
            ),
          ),
        ],
      ),
    );
  }
}

class _PageIndicator extends StatelessWidget {
  // Using direct color values as recommended, as they are not in the generated tokens yet.
  final Color _activeColor = DesignTokens.textPrimary; // Correct reference
  final Color _inactiveColor =
      const Color(0xFF333333); // Using direct value as discussed

  final int pageCount;
  final int currentPageIndex;

  const _PageIndicator({
    required this.pageCount,
    required this.currentPageIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(pageCount, (index) {
        final isActive = index == currentPageIndex;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
          margin: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingSm / 2),
          height: 8,
          width: isActive ? 24 : 8,
          decoration: BoxDecoration(
            color: isActive ? _activeColor : _inactiveColor,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          ),
        );
      }),
    );
  }
}
