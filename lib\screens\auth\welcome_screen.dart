// lib/screens/auth/welcome_screen.dart

// Assets needed:
// assets/images/darvis_main.PNG

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/screens/home/<USER>'; // The destination screen

// Reusable asset path constant
const String _kDarvisLogo = 'assets/images/darvis_main.PNG';

/// WelcomeScreen: A temporary screen displayed after successful sign-up.
/// It shows a welcome message and automatically transitions to the HomeScreen.
class WelcomeScreen extends StatefulWidget {
  /// The name of the user to be displayed.
  final String name;

  const WelcomeScreen({
    super.key,
    required this.name,
  });

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  @override
  void initState() {
    super.initState();
    _startTransitionTimer();
  }

  /// Initiates a timer to navigate to the HomeScreen after a delay.
  void _startTransitionTimer() {
    // The duration is set to 5 seconds as requested.
    Timer(const Duration(seconds: 10), () {
      // Ensure the widget is still in the tree before navigating.
      if (mounted) {
        Navigator.of(context).pushReplacement(
          // Using PageRouteBuilder for a custom cross-fade transition.
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const HomeScreen(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration:
                const Duration(milliseconds: 700), // Duration of the fade
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: Stack(
        children: [
          // Reusing the same background glow from previous screens.
          const _BackgroundGlow(),

          // The main content, centered on the screen.
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const SizedBox(height: 250,),
                // The main logo with its hover animation.
                const _AnimatedDarvisLogo(),

                // Spacing between the logo and welcome text, as recommended.
                const SizedBox(height: DesignTokens.spacingXl),

                // The "Welcome [Name]!" text.
                _WelcomeText(name: widget.name),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// A stateful widget to handle the subtle hover/bounce animation for the main logo.
class _AnimatedDarvisLogo extends StatefulWidget {
  const _AnimatedDarvisLogo();

  @override
  State<_AnimatedDarvisLogo> createState() => _AnimatedDarvisLogoState();
}

class _AnimatedDarvisLogoState extends State<_AnimatedDarvisLogo>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween<Offset>(
      begin: const Offset(0, -0.08),
      end: const Offset(0, 0.008),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: Image.asset(
        _kDarvisLogo,
        height: 200, // Adjust height as needed for visual balance
      ),
    );
  }
}

/// A widget to display the "Welcome [Name]!" text with styled parts.
class _WelcomeText extends StatelessWidget {
  final String name;
  const _WelcomeText({required this.name});

  @override
  Widget build(BuildContext context) {
    // Using RichText to apply different styles to different parts of the text.
    return RichText(
      text: TextSpan(
        // The default style for the entire text block, using our new token.
        style: DesignTokens.welcomeHeadlineStyle,
        children: [
          // The "Welcome " part, which will inherit the default style (white).
          const TextSpan(text: 'Welcome '),

          // The dynamic name part, with a different color.
          TextSpan(
            text: '$name!',
            style: const TextStyle(
              // Using the interactive blue color as requested.
              color: DesignTokens.primaryInteractiveBlue,
            ),
          ),
        ],
      ),
    );
  }
}

/// The decorative background glow effect, reused from previous screens.
class _BackgroundGlow extends StatelessWidget {
  const _BackgroundGlow();

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.2,
        decoration: const BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color.fromARGB(255, 9, 38, 107),
              blurRadius: 150.0,
              spreadRadius: 50.0,
            ),
          ],
        ),
      ),
    );
  }
}
