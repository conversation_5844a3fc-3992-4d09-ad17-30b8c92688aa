The Darvis Frontend: The Definitive Technical Build Plan (v3)

Project: <PERSON><PERSON>end - The User Interface for the Conversational AI Second Brain
Target: AI Coding Agent for Flutter/Dart
Guiding Document: This document serves as the complete architectural blueprint and implementation guide. It is designed to work in tandem with the ARCHITECTURE_OVERVIEW.md from the darvis-backend project.

1. Core Frontend Principles & Philosophy

This plan translates the core Darvis philosophy into concrete frontend goals:

Conversational First: The UI must prioritize immediate and intuitive access to the chat and voice interfaces. The user should feel that conversing with <PERSON><PERSON> is the primary, most natural action to take.

Context is King: The UI must be state-aware and dynamically adapt. The visual presentation, available actions, and even subtle animations will change based on the user's selected "Mode" (General, Therapy, Learning), creating a tailored and intelligent experience.

Frictionless Integration & Resilience: The experience must be fast, seamless, and robust. This includes a comprehensive offline-first strategy and graceful error handling to ensure core features are always available and feel instantaneous, even under poor network conditions.

2. Definitive Frontend Technology Stack

This stack is chosen for performance, security, developer experience, and its ability to create a high-quality, native, cross-platform application.

Category	Technology/Library	Why?
Core Framework	Flutter	For building a single, high-performance, natively compiled application for both iOS and Android from one codebase.
State Management	BLoC (flutter_bloc)	Provides a robust, scalable, and highly testable way to separate business logic from the UI, essential for managing the complex states of Darvis.
API Communication	Dio, dio_retry	A powerful HTTP client with built-in support for interceptors, advanced error handling, and exponential backoff for retries.
Real-time Voice	LiveKit Client SDK (livekit_client)	The official, high-performance SDK for connecting to our LiveKit backend, handling all complex WebRTC logic.
Authentication	Firebase Auth (firebase_auth)	The official Flutter SDK for our chosen authentication provider.
Notifications	Firebase Messaging (firebase_messaging)	The official SDK for receiving Firebase Cloud Messaging (FCM) push notifications.
Local Caching & Offline Storage	Isar Database	An extremely fast, full-featured NoSQL database for Flutter. It is the cornerstone of our offline-first strategy.
Service Location	get_it	A simple and powerful dependency injection tool for easily accessing services from anywhere in the app.
Security	flutter_secure_storage, dio_certificate_pinning, encrypt	For storing sensitive tokens in the secure enclave, preventing man-in-the-middle attacks, and encrypting local data at rest.
Performance	firebase_performance, cached_network_image	To monitor app performance, track API latency, and efficiently cache network images.
Testing	flutter_test, bloc_test, mocktail, golden_toolkit	A comprehensive suite for unit, BLoC, widget, and "golden" (UI snapshot) testing.
Error Reporting	firebase_crashlytics	For robust, real-time crash reporting to quickly identify and fix bugs.
Build Automation	build_runner, openapi-generator-cli	For auto-generating Dart models from our backend's OpenAPI schema and managing design tokens.
3. Detailed Project Structure & Scaffolding

This is the blueprint for the project's file structure to be created by the agent.

Generated code
darvis_app/
├── lib/
│   ├── main.dart             // Main app entry point, initializes services, Firebase.
│   ├── screens/              // Contains all the primary UI screens.
│   │   ├── auth/             // Login, sign-up screens.
│   │   ├── home/             // Main dashboard/mode selection screen.
│   │   ├── chat/             // The text-based conversation screen.
│   │   ├── voice/            // The real-time voice interaction screen.
│   │   ├── productivity/     // Screens for Tasks, Notes, Contacts, Lessons.
│   │   └── ... (Inbox, Therapy, etc.)
│   ├── widgets/              // Reusable UI components.
│   │   └── common/           // Buttons, text fields, loading indicators, etc.
│   ├── services/             // Handles business logic and external communication.
│   │   ├── api_service.dart  // Handles all backend HTTP requests via Dio.
│   │   ├── auth_service.dart // Handles all Firebase authentication logic.
│   │   ├── livekit_service.dart// Manages real-time voice connection.
│   │   ├── sync_engine.dart  // (Offline Strategy) Handles queuing and syncing local changes.
│   │   └── ... (NotificationService, etc.)
│   ├── models/               // (Auto-generated) Dart classes for our data.
│   ├── blocs/                // The BLoC state management layer.
│   │   ├── auth/             // BLoC for authentication state.
│   │   └── ... (BLoCs for each feature)
│   └── utils/                // Helper functions, constants, theme data.
│       └── design_tokens.dart // (Auto-generated) from design_tokens.yaml.
├── test/
│   ├── mock_impl/            // In-memory fake services for UI testing.
│   └── factories/            // Generates realistic mock data for tests.
├── design_tokens.yaml        // Single source of truth for all UI constants.
└── PROGRESS_TRACKER.md       // The development progress log.

4. Phased Implementation Plan

This breaks down the build into manageable, logical phases.

Phase 0: Project Foundation & Production Readiness

Goal: Establish a world-class, secure, and maintainable development foundation.

Steps:

Initialize Project: Create the Flutter project and the full directory structure above.

Add Dependencies: Populate pubspec.yaml with all planned libraries.

Developer Experience: Set up build_runner for model generation and design tokens. Create mock_impl/ for fake services and test/factories/ with faker for realistic mock data.

Security Foundation: Configure flutter_secure_storage as the only mechanism for storing refresh tokens. Implement dio_certificate_pinning in the ApiService from day one.

Phase 1: Secure, Offline-First Foundation & Core UI

Goal: Build a stable app with a robust offline and authentication system.

Steps:

Service Setup & Auth: Implement get_it, Dio with interceptors, and the full Firebase Auth flow using AuthService and AuthBloc.

Robust Offline Sync Engine: Implement the SyncEngine with a detailed strategy:

Write Queue: All writes go to Isar first with a pending-sync status.

Retry Logic: Use dio_retry with an exponential backoff strategy to automatically re-submit failed requests when connectivity returns.

Conflict Resolution: Implement a simple "last write wins" strategy. If a sync fails due to a conflict, the server's version will be fetched and will overwrite the local version.

UI Feedback: A global SyncStatus stream will provide UI feedback (Synced, Syncing..., Offline).

Local Data Encryption: Use the encrypt package to implement type converters for Isar, ensuring that all sensitive data is stored encrypted on the user's device.

Productivity UI: Build the full UI for Tasks and Notes. Implement lazy loading (pagination) for all lists. The UI must be offline-first, reading from and writing to the local Isar database.

Accessibility & Error Handling: Add semantic labels to all widgets. Implement inline error messages with "Retry" buttons for failed operations.

Phase 2: The Resilient Conversational Interface

Goal: Build the text-based chat experience with graceful error handling and performance monitoring.

Steps:

Chat UI & BLoC: Build the conversation screen.

Performance Optimization: Move all JSON parsing for chat messages into a background Isolate to prevent UI stutter.

Error Boundaries & Crashlytics: Implement graceful degradation for streaming interruptions and backend downtime. Integrate Firebase Crashlytics to automatically report any crashes in the chat interface.

Phase 3: The Advanced Voice Experience & Final Polish

Goal: Implement the real-time voice interface and round out the remaining features.

Steps:

LiveKit Integration: Integrate the LiveKit SDK.

Voice UX & Error Handling: Build the voice UI with clear visual feedback states for listening, transcribing, thinking, and speaking. The system must handle platform-specific microphone permissions gracefully and have a clear fallback strategy to text chat for poor network conditions.

Final Feature UIs & Testing: Build the UIs for all remaining features. Write end-to-end integration tests for the most critical user journeys (e.g., login -> create task -> go offline -> go online -> verify sync).
