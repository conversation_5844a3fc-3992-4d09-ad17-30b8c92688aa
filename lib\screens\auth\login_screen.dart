import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:darvis_app/utils/design_tokens.dart';

// Reusable asset path constants
const String _kDarvisLogo = 'assets/images/darvis_main.PNG';
const String _kGoogleLogo = 'assets/icons/google_logo.svg';
const String _kEyeOnIcon = 'assets/icons/eye_on.svg';
const String _kEyeOffIcon = 'assets/icons/eye_off.svg';

/// LoginScreen: A screen for existing user authentication.
/// It reuses many components from the SignUpScreen for consistency.
class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: Stack(
        children: [
          // Reusing the same background glow from the sign-up screen.
          const _BackgroundGlow(),

          LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spacingXl),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Main Darvis logo with hover animation.
                        const _AnimatedDarvisLogo(),
                        const SizedBox(height: DesignTokens.spacingXxl),

                        // Reusing the social button for Gmail login.
                        _SocialLoginButton(
                          iconPath: _kGoogleLogo,
                          text: 'Gmail', // As per the design
                          onTap: () {
                            print('Gmail Login Tapped');
                          },
                        ),
                        const SizedBox(height: DesignTokens.spacingXl),

                        // The main login form.
                        const _LoginForm(),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// A stateful widget to handle the subtle hover/bounce animation for the main logo.
class _AnimatedDarvisLogo extends StatefulWidget {
  const _AnimatedDarvisLogo();

  @override
  State<_AnimatedDarvisLogo> createState() => _AnimatedDarvisLogoState();
}

class _AnimatedDarvisLogoState extends State<_AnimatedDarvisLogo>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween<Offset>(
      begin: const Offset(0, -0.08),
      end: const Offset(0, 0.08),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: Image.asset(
        _kDarvisLogo,
        height: 200, // Adjust height as needed for visual balance
      ),
    );
  }
}

/// The main form for the login screen.
class _LoginForm extends StatefulWidget {
  const _LoginForm();

  @override
  State<_LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<_LoginForm> {
  bool _isPasswordObscured = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _TitledTextField(
          title: 'Email Address',
          hintText: '<EMAIL>',
          keyboardType: TextInputType.emailAddress,
        ),
        const SizedBox(height: DesignTokens.spacingMd),
        _TitledTextField(
          title: 'Password',
          hintText: '••••••••',
          isObscured: _isPasswordObscured,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              _isPasswordObscured ? _kEyeOffIcon : _kEyeOnIcon,
              colorFilter: const ColorFilter.mode(
                  DesignTokens.iconSecondary, BlendMode.srcIn),
            ),
            onPressed: () =>
                setState(() => _isPasswordObscured = !_isPasswordObscured),
          ),
        ),
        const SizedBox(height: DesignTokens.spacingLg),
        _ForgotPasswordLink(
          onTap: () {
            print('Forgot Password Tapped');
          },
        ),
        const SizedBox(height: DesignTokens.spacingLg),
        _PrimaryActionButton(
          text: 'Sign In',
          onTap: () {
            print('Sign In Tapped');
          },
        ),
      ],
    );
  }
}

// --- Reused Widgets (Adapted from your tweaked SignUpScreen code) ---

class _SocialLoginButton extends StatelessWidget {
  final String iconPath;
  final String text;
  final VoidCallback onTap;

  const _SocialLoginButton(
      {required this.iconPath, required this.text, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        height: 46,
        decoration: BoxDecoration(
          color: DesignTokens.googleButtonBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath, height: 24, width: 24),
            const SizedBox(width: DesignTokens.spacingMd),
            Text(text, style: DesignTokens.googleButtonTextStyle),
          ],
        ),
      ),
    );
  }
}

class _TitledTextField extends StatelessWidget {
  final String title;
  final String hintText;
  final bool isObscured;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;

  const _TitledTextField({
    required this.title,
    required this.hintText,
    this.isObscured = false,
    this.suffixIcon,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),
        TextField(
          obscureText: isObscured,
          keyboardType: keyboardType,
          style:
              DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            hintText: hintText,
            hintStyle:
                DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm,
            ),
          ),
        ),
      ],
    );
  }
}

class _ForgotPasswordLink extends StatelessWidget {
  final VoidCallback onTap;
  const _ForgotPasswordLink({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Center(
        child: Text(
          'Forgot Password?',
          style: DesignTokens.linkTextStyle
              .copyWith(color: DesignTokens.linkColor),
        ),
      ),
    );
  }
}

class _PrimaryActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  const _PrimaryActionButton({required this.text, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 150,
          height: 45,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: DesignTokens.primaryButtonBackground,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          ),
          child: Text(text, style: DesignTokens.primaryButtonTextStyle),
        ),
      ),
    );
  }
}

class _BackgroundGlow extends StatelessWidget {
  const _BackgroundGlow();

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.1,
        decoration: const BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color.fromARGB(255, 9, 38, 107),
              blurRadius: 150.0,
              spreadRadius: 50.0,
            ),
          ],
        ),
      ),
    );
  }
}
