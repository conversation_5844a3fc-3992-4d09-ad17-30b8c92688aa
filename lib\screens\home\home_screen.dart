import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:darvis_app/utils/design_tokens.dart';

// Reusable asset path constants
const String _kUserProfilePic = 'assets/images/profile_pic.png';
const String _kGreetingIcon = 'assets/icons/morning.svg';
const String _kNotificationIcon = 'assets/icons/notification.svg';
const String _kDarvisCardLogo = 'assets/images/darvisintelligence.png';

/// HomeScreen: The central hub of the application after login.
/// Displays user greeting, a summary card, mode selection, and main navigation.
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // A GlobalKey is needed to open the endDrawer programmatically.
    final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

    // Dummy data for display purposes, to be replaced by BLoC state.
    const userName = 'David';
    const List<String> todayTasks = ['Talk to De<PERSON><PERSON>', 'Grocery Shopping'];

    return Scaffold(
      key: scaffold<PERSON>ey,
      backgroundColor: DesignTokens.backgroundApp,
      // The endDrawer is a panel that slides in from the right.
      endDrawer: const _NotificationDrawer(),
      body: SafeArea(
        child: Column(
          children: [
            // Top App Bar section
            _TopAppBar(
              userName: userName,
              greetingIconPath: _kGreetingIcon,
              onNotificationsTapped: () =>
                  scaffoldKey.currentState?.openEndDrawer(),
            ),

            // Main scrollable content area
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spacingLg),
                children: [
                  const SizedBox(height: DesignTokens.spacingLg),
                  _TodaysPlateCard(
                    date: 'Monday, 16th July',
                    tasks: todayTasks,
                    onTap: () {
                      print('Todays Plate Tapped');
                    },
                  ),
                  const SizedBox(height: DesignTokens.spacingXl),
                  _ModeSelectionGrid(
                    onLearningHubTapped: () {
                      print('Learning Hub Tapped');
                    },
                    onPersonalSpaceTapped: () {
                      print('Personal Space Tapped');
                    },
                    onProductivityTapped: () {
                      print('Productivity Tapped');
                    },
                    onUpcomingEventsTapped: () {
                      print('Upcoming Events Tapped');
                    },
                  ),
                  const SizedBox(
                      height: DesignTokens
                          .spacingXxl), // Space before bottom nav bar
                ],
              ),
            ),
          ],
        ),
      ),
      // Custom bottom navigation bar
      bottomNavigationBar: _CustomBottomNavBar(
        selectedIndex: 0, // 'Home' is active by default
        onItemSelected: (index) {
          print('Tapped nav item: $index');
        },
      ),
    );
  }
}

// --- Top App Bar and its components ---

class _TopAppBar extends StatelessWidget {
  final String userName;
  final String greetingIconPath;
  final VoidCallback onNotificationsTapped;

  const _TopAppBar({
    required this.userName,
    required this.greetingIconPath,
    required this.onNotificationsTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
        DesignTokens.spacingLg,
        DesignTokens.spacingMd,
        DesignTokens.spacingLg,
        DesignTokens.spacingMd,
      ),
      child: Row(
        children: [
          const CircleAvatar(
            radius: 24,
            backgroundImage: AssetImage(_kUserProfilePic),
          ),
          const SizedBox(width: DesignTokens.spacingMd),
          Expanded(
            child: Row(
              children: [
                RichText(
                  text: TextSpan(
                    style: DesignTokens.greetingTextStyle,
                    children: [
                      const TextSpan(text: 'Good Morning '),
                      TextSpan(
                        text: userName,
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 4), // Reduce spacing here
                SvgPicture.asset(greetingIconPath, height: 24, width: 24),
              ],
            ),
          ),
          const SizedBox(width: DesignTokens.spacingXxl),
          IconButton(
            icon: SvgPicture.asset(_kNotificationIcon, height: 22, width: 22),
            onPressed: onNotificationsTapped,
          ),
        ],
      ),
    );
  }
}

// --- "Today's Plate" Card and its components ---

class _TodaysPlateCard extends StatelessWidget {
  final String date;
  final List<String> tasks;
  final VoidCallback onTap;

  const _TodaysPlateCard({
    required this.date,
    required this.tasks,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(DesignTokens.spacingLg),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          gradient: DesignTokens.todayCardBackgroundGradient,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(date, style: DesignTokens.cardLargeHeadingStyle),
                  const SizedBox(height: DesignTokens.spacingXs),
                  Text('Here\'s what\'s on your plate today',
                      style: DesignTokens.cardSubheadingStyle),
                  const SizedBox(height: DesignTokens.spacingMd),
                  ...tasks.map((task) => Padding(
                        padding: const EdgeInsets.only(
                            bottom: DesignTokens.spacingXs),
                        child: Text('• $task',
                            style: DesignTokens.cardListItemStyle),
                      )),
                ],
              ),
            ),
            const SizedBox(width: DesignTokens.spacingMd),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const _AnimatedCardLogo(),
                const SizedBox(height: DesignTokens.spacingLg),
                Text('Tap for More', style: DesignTokens.linkTextStyle),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _AnimatedCardLogo extends StatefulWidget {
  const _AnimatedCardLogo();

  @override
  State<_AnimatedCardLogo> createState() => _AnimatedCardLogoState();
}

class _AnimatedCardLogoState extends State<_AnimatedCardLogo>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 5), // Slower animation as requested
      vsync: this,
    )..repeat(reverse: true);
    _animation = Tween<Offset>(
      begin: const Offset(0, -0.02),
      end: const Offset(0, 0.02),
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: Image.asset(_kDarvisCardLogo, height: 80),
    );
  }
}

// --- Mode Selection Grid ---

class _ModeSelectionGrid extends StatelessWidget {
  final VoidCallback onLearningHubTapped;
  final VoidCallback onPersonalSpaceTapped;
  final VoidCallback onProductivityTapped;
  final VoidCallback onUpcomingEventsTapped;

  const _ModeSelectionGrid({
    required this.onLearningHubTapped,
    required this.onPersonalSpaceTapped,
    required this.onProductivityTapped,
    required this.onUpcomingEventsTapped,
  });

  @override
  Widget build(BuildContext context) {
    // Using a GridView for the 2x2 layout.
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: DesignTokens.spacingMd,
      mainAxisSpacing: DesignTokens.spacingMd,
      childAspectRatio: 1.0, // Makes the cards square
      children: [
        _ModeCard(
          title: 'Learning Hub',
          color: DesignTokens.primaryDeepBlue,
          onTap: onLearningHubTapped,
        ),
        _ModeCard(
          title: 'Personal Space',
          color: DesignTokens.primaryAccentBlue,
          onTap: onPersonalSpaceTapped,
        ),
        _ModeCard(
          title: 'Productivity',
          color: DesignTokens.primaryAccentBlue,
          onTap: onProductivityTapped,
        ),
        _ModeCard(
          title: 'Upcoming Events',
          color: DesignTokens.primaryDeepBlue,
          onTap: onUpcomingEventsTapped,
        ),
      ],
    );
  }
}

class _ModeCard extends StatelessWidget {
  final String title;
  final Color color;
  final VoidCallback onTap;

  const _ModeCard(
      {required this.title, required this.color, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          // Note: Inner shadows are complex. A simple border or gradient can be used
          // instead to avoid performance issues. Ditching the effect as requested.
        ),
        padding: const EdgeInsets.all(DesignTokens.spacingMd),
        alignment: Alignment.center,
        child: Text(title,
            style: DesignTokens.cardTitleStyle, textAlign: TextAlign.center),
      ),
    );
  }
}

// --- Custom Bottom Navigation Bar ---

class _CustomBottomNavBar extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onItemSelected;

  const _CustomBottomNavBar(
      {required this.selectedIndex, required this.onItemSelected});

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: DesignTokens.backgroundApp,
      elevation: 0,
      child: SizedBox(
        height: 70,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(
                index: 0,
                iconPath: 'assets/icons/home_icone.svg',
                label: 'Home'),
            _buildNavItem(
                index: 1,
                iconPath: 'assets/icons/inbox_icon.svg',
                label: 'Inbox'),
            _buildCentralNavItem(
                index: 2, iconPath: 'assets/icons/darvisnavbar_icon.svg'),
            _buildNavItem(
                index: 3,
                iconPath: 'assets/icons/search_icon.svg',
                label: 'Search'),
            _buildNavItem(
                index: 4,
                iconPath: 'assets/icons/profile_icon.svg',
                label: 'Profile'),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(
      {required int index, required String iconPath, required String label}) {
    final bool isActive = selectedIndex == index;
    final Color color = isActive
        ? DesignTokens.primaryInteractiveBlue
        : DesignTokens.navigationInactive;

    return Expanded(
      child: InkWell(
        onTap: () => onItemSelected(index),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath,
                colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                height: 24),
            const SizedBox(height: DesignTokens.spacingXs),
            Text(label,
                style: DesignTokens.navBarLabelStyle.copyWith(color: color)),
          ],
        ),
      ),
    );
  }

  Widget _buildCentralNavItem({required int index, required String iconPath}) {
    final bool isActive = selectedIndex == index;
    // Using a ShaderMask to apply a gradient to the background circle
    return Expanded(
      child: InkWell(
        onTap: () => onItemSelected(index),
        child: ShaderMask(
          shaderCallback: (bounds) {
            return (isActive
                    ? DesignTokens.backgroundGlowBlueGradient
                    : DesignTokens.appNameHeaderGradient)
                .createShader(bounds);
          },
          child: Container(
            height: 60,
            width: 60,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors
                  .white, // This color is irrelevant, the shader will paint over it.
            ),
            child: Center(
              child: SvgPicture.asset(iconPath, height: 32),
            ),
          ),
        ),
      ),
    );
  }
}

// --- Notification Drawer ---

class _NotificationDrawer extends StatelessWidget {
  const _NotificationDrawer();

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: MediaQuery.of(context).size.width *
          0.6, // Using a bit more than 40% for better content fit
      backgroundColor: DesignTokens.backgroundCard,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const SizedBox(
            height: 120,
            child: DrawerHeader(
              decoration: BoxDecoration(color: DesignTokens.primaryDeepBlue),
              child: Text('Notifications', style: DesignTokens.cardTitleStyle),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.info_outline,
                color: DesignTokens.iconSecondary),
            title:
                const Text('Welcome to Darvis!', style: DesignTokens.bodyStyle),
            subtitle: const Text('Your setup is complete.',
                style: DesignTokens.navBarLabelStyle),
            onTap: () {/* Handle tap */},
          ),
          const Divider(
              color: DesignTokens.borderInteractiveElement, height: 1),
          ListTile(
            leading:
                const Icon(Icons.task_alt, color: DesignTokens.iconSecondary),
            title:
                const Text('New Task Assigned', style: DesignTokens.bodyStyle),
            subtitle: const Text('Review the project brief.',
                style: DesignTokens.navBarLabelStyle),
            onTap: () {/* Handle tap */},
          ),
          const Divider(
              color: DesignTokens.borderInteractiveElement, height: 1),
          ListTile(
            leading: const Icon(Icons.summarize_outlined,
                color: DesignTokens.iconSecondary),
            title: const Text('Article Summary Ready',
                style: DesignTokens.bodyStyle),
            subtitle: const Text('Your summary for "AI in 2025" is available.',
                style: DesignTokens.navBarLabelStyle),
            onTap: () {/* Handle tap */},
          ),
        ],
      ),
    );
  }
}
