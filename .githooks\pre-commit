#!/bin/bash

# Pre-commit hook to auto-update CODEMAP.md
# This ensures the code map stays fresh on every commit

echo "🗺️  Updating CODEMAP.md before commit..."

# Check if dart is available
if ! command -v dart &> /dev/null; then
    echo "⚠️  Dart not found. Skipping CODEMAP.md update."
    exit 0
fi

# Generate updated CODEMAP.md
dart run tool/gen_codemap.dart

# Check if CODEMAP.md was modified
if git diff --quiet CODEMAP.md; then
    echo "✅ CODEMAP.md is up to date"
else
    echo "📝 CODEMAP.md updated - adding to commit"
    git add CODEMAP.md
fi

echo "🚀 Pre-commit hook completed successfully"
exit 0
