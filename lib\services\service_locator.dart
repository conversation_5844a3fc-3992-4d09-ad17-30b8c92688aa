import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'api_service.dart';
import 'auth_service.dart';
import 'livekit_service.dart';
import 'sync_engine.dart';
import '../blocs/auth/auth_bloc.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Core services
  getIt.registerLazySingleton<Dio>(() => _createDio());
  getIt.registerLazySingleton<FlutterSecureStorage>(
    () => const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    ),
  );

  // Business services
  getIt.registerLazySingleton<ApiService>(
    () => ApiService(getIt<Dio>()),
  );
  
  getIt.registerLazySingleton<AuthService>(
    () => AuthService(
      apiService: getIt<ApiService>(),
      secureStorage: getIt<FlutterSecureStorage>(),
    ),
  );
  
  getIt.registerLazySingleton<LiveKitService>(
    () => LiveKitService(),
  );
  
  getIt.registerLazySingleton<SyncEngine>(
    () => SyncEngine(
      apiService: getIt<ApiService>(),
    ),
  );

  // BLoCs
  getIt.registerFactory<AuthBloc>(
    () => AuthBloc(
      authService: getIt<AuthService>(),
    ),
  );
}

Dio _createDio() {
  final dio = Dio();
  
  // Base configuration
  dio.options.baseUrl = 'https://api.darvis.app'; // TODO: Replace with actual API URL
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);
  dio.options.sendTimeout = const Duration(seconds: 30);

  // Add interceptors
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    logPrint: (object) {
      // TODO: Use proper logging service
      print(object);
    },
  ));

  // TODO: Add authentication interceptor
  // TODO: Add certificate pinning
  // TODO: Add retry interceptor

  return dio;
}
