# Darvis AI Assistant - Flutter Frontend

A conversational AI second brain built with Flutter, featuring real-time voice interaction, offline-first architecture, and a beautiful dark UI.

## 🚀 Project Status

**Phase 0: COMPLETED** ✅  
Foundation established with production-ready architecture, security measures, and comprehensive testing infrastructure.

See [PROGRESS_TRACKER.md](PROGRESS_TRACKER.md) for detailed progress information.

## 🏗️ Architecture

### Core Principles
- **Conversational First**: Prioritizes chat and voice interfaces
- **Context Aware**: Adapts UI based on user modes (General, Therapy, Learning)
- **Offline First**: Robust sync engine with local data storage
- **Security Focused**: Encrypted storage and certificate pinning

### Technology Stack
- **Framework**: Flutter 3.13+
- **State Management**: B<PERSON>o<PERSON>tern
- **API Communication**: Dio with retry logic
- **Real-time Voice**: LiveKit WebRTC
- **Authentication**: Firebase Auth
- **Local Storage**: Isar Database
- **Security**: flutter_secure_storage, encryption
- **Testing**: bloc_test, mocktail, golden_toolkit

## 📁 Project Structure

```
lib/
├── main.dart              # App entry point
├── screens/               # UI screens
│   ├── auth/             # Authentication
│   ├── home/             # Dashboard
│   ├── chat/             # Text conversations
│   ├── voice/            # Voice interactions
│   └── productivity/     # Tasks & Notes
├── widgets/              # Reusable components
├── services/             # Business logic
│   ├── api_service.dart  # HTTP client
│   ├── auth_service.dart # Authentication
│   ├── livekit_service.dart # Voice communication
│   └── sync_engine.dart  # Offline sync
├── blocs/                # State management
├── models/               # Data models
└── utils/                # Utilities & design tokens
```

## 🎨 Design System

The app uses a comprehensive design token system based on a dark, futuristic theme:

- **Colors**: Deep blues with accent highlights
- **Typography**: Outfit (headings) + Poppins (body)
- **Gradients**: Radial glows and linear text effects
- **Components**: Cards, buttons, chat bubbles

Design tokens are defined in `design_tokens.yaml` and generated into type-safe Dart code.

## 🔐 Security Features

- **Secure Storage**: Tokens stored in device secure enclave
- **Certificate Pinning**: Protection against MITM attacks
- **Local Encryption**: Sensitive data encrypted at rest
- **Firebase Auth**: Industry-standard authentication

## 🔄 Offline-First Strategy

- **Local Database**: Isar for fast, offline data access
- **Sync Engine**: Queue-based operation management
- **Conflict Resolution**: Last-write-wins strategy
- **Retry Logic**: Exponential backoff for failed operations

## 🧪 Testing

Comprehensive testing infrastructure:

- **Mock Services**: Complete fake implementations
- **Data Factories**: Realistic test data generation
- **BLoC Testing**: State management testing
- **Golden Tests**: UI snapshot testing

## 🚀 Getting Started

### Prerequisites
- Flutter 3.13 or higher
- Dart 3.1 or higher
- Firebase project setup

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd darvis-app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Firebase**
   - Add your `google-services.json` (Android)
   - Add your `GoogleService-Info.plist` (iOS)

4. **Run the app**
   ```bash
   flutter run
   ```

### Development Setup

1. **Generate code** (when needed)
   ```bash
   flutter packages pub run build_runner build
   ```

2. **Run tests**
   ```bash
   flutter test
   ```

3. **Run with mock data**
   The app includes comprehensive mock services for development and testing.

## 📱 Features

### Current (Phase 0)
- ✅ Authentication (Firebase + Backend)
- ✅ Secure token management
- ✅ Design system implementation
- ✅ Offline-first architecture
- ✅ Mock services for testing

### Planned (Phase 1)
- 🔄 Task and Note management
- 🔄 Real-time chat interface
- 🔄 Offline sync implementation
- 🔄 Error handling and recovery

### Future (Phase 2+)
- 🔄 Voice interaction (LiveKit)
- 🔄 Mode-based UI adaptation
- 🔄 Advanced AI features

## 🤝 Contributing

1. Follow the established architecture patterns
2. Use the design token system for all UI elements
3. Write tests for new features
4. Update the progress tracker

## 📄 License

[Add your license information here]

## 🔗 Related Projects

- [Darvis Backend](../darvis-backend) - The AI backend service
- [Design System](design_tokens.yaml) - UI design specifications

---

**Built with ❤️ for the future of conversational AI**
