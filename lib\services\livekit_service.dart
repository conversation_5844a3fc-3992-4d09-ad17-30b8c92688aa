import 'dart:async';
import 'package:livekit_client/livekit_client.dart';

/// Service for handling real-time voice communication with LiveKit
class LiveKitService {
  Room? _room;
  LocalAudioTrack? _audioTrack;
  EventsListener<RoomEvent>? _roomListener;
  bool _isConnected = false;
  bool _isMuted = false;

  final StreamController<bool> _connectionStateController = StreamController<bool>.broadcast();
  final StreamController<bool> _muteStateController = StreamController<bool>.broadcast();
  final StreamController<String> _transcriptionController = StreamController<String>.broadcast();

  /// Stream of connection state changes
  Stream<bool> get connectionStateStream => _connectionStateController.stream;

  /// Stream of mute state changes
  Stream<bool> get muteStateStream => _muteStateController.stream;

  /// Stream of transcription updates
  Stream<String> get transcriptionStream => _transcriptionController.stream;

  /// Check if connected to room
  bool get isConnected => _isConnected;

  /// Check if microphone is muted
  bool get isMuted => _isMuted;

  /// Connect to LiveKit room
  Future<void> connect({
    required String url,
    required String token,
    required String roomName,
  }) async {
    try {
      // Create room
      _room = Room();

      // Set up event listeners
      _room!.addListener(_onRoomEvent);

      // Connect to room
      await _room!.connect(
        url,
        token,
        roomOptions: const RoomOptions(
          adaptiveStream: true,
          dynacast: true,
          defaultAudioPublishOptions: AudioPublishOptions(
            name: 'microphone',
          ),
        ),
      );

      // Enable microphone
      await _enableMicrophone();

      _isConnected = true;
      _connectionStateController.add(true);
    } catch (e) {
      _isConnected = false;
      _connectionStateController.add(false);
      rethrow;
    }
  }

  /// Disconnect from room
  Future<void> disconnect() async {
    try {
      await _audioTrack?.stop();
      await _room?.disconnect();

      _roomListener?.dispose();
      _roomListener = null;
      _room = null;
      _audioTrack = null;
      
      _isConnected = false;
      _isMuted = false;
      
      _connectionStateController.add(false);
      _muteStateController.add(false);
    } catch (e) {
      // Log error but don't throw
      print('Error disconnecting from LiveKit: $e');
    }
  }

  /// Toggle microphone mute state
  Future<void> toggleMute() async {
    if (_audioTrack == null) return;

    try {
      if (_isMuted) {
        await _audioTrack!.unmute();
        _isMuted = false;
      } else {
        await _audioTrack!.mute();
        _isMuted = true;
      }
      
      _muteStateController.add(_isMuted);
    } catch (e) {
      print('Error toggling mute: $e');
      rethrow;
    }
  }

  /// Start speaking (unmute if muted)
  Future<void> startSpeaking() async {
    if (_isMuted) {
      await toggleMute();
    }
  }

  /// Stop speaking (mute)
  Future<void> stopSpeaking() async {
    if (!_isMuted) {
      await toggleMute();
    }
  }

  /// Enable microphone and start publishing audio
  Future<void> _enableMicrophone() async {
    try {
      // Create audio track
      _audioTrack = await LocalAudioTrack.create(AudioCaptureOptions(
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      ));

      // Publish audio track
      await _room!.localParticipant?.publishAudioTrack(_audioTrack!);
      
      _isMuted = false;
      _muteStateController.add(false);
    } catch (e) {
      print('Error enabling microphone: $e');
      rethrow;
    }
  }

  /// Handle room events
  void _onRoomEvent() {
    _room!.addListener(() {
      switch (_room!.connectionState) {
        case ConnectionState.connected:
          _isConnected = true;
          _connectionStateController.add(true);
          break;
        case ConnectionState.disconnected:
          _isConnected = false;
          _connectionStateController.add(false);
          break;
        case ConnectionState.connecting:
        case ConnectionState.reconnecting:
          // Handle connecting states if needed
          break;
      }
    });

    // Listen for participant events using modern EventsListener API
    _roomListener = _room!.createListener();
    _roomListener!
      ..on<ParticipantConnectedEvent>((event) {
        print('Participant connected: ${event.participant.identity}');
        _setupParticipantListener(event.participant);
      })
      ..on<RoomDisconnectedEvent>((event) {
        print('Room disconnected');
      });
  }

  /// Set up listener for a specific participant
  void _setupParticipantListener(Participant participant) {
    participant.addListener(() {
      // Handle participant track events using modern API
      for (final track in participant.audioTrackPublications) {
        if (track.track != null && !track.muted) {
          // Handle audio track (e.g., AI voice response)
          print('Received audio track from ${participant.identity}');
        }
      }
    });
  }

  /// Send transcription data (if implementing real-time transcription)
  void sendTranscription(String text) {
    _transcriptionController.add(text);
  }

  /// Dispose of resources
  void dispose() {
    _connectionStateController.close();
    _muteStateController.close();
    _transcriptionController.close();
    disconnect();
  }
}
