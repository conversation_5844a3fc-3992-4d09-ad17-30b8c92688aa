import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:darvis_app/utils/design_tokens.dart';

// Reusable asset path constants
const String _kGoogleLogo = 'assets/icons/google_logo.svg';
const String _kEyeOnIcon = 'assets/icons/eye_on.svg';
const String _kEyeOffIcon = 'assets/icons/eye_off.svg';

/// SignUpScreen: A screen for new user registration.
/// Accepts callbacks for user actions to be handled by a parent BLoC.
class SignUpScreen extends StatelessWidget {
  const SignUpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: Stack(
        children: [
          // The background glow effect, positioned at the bottom.
          // Adjusted to be more subtle and compact as per feedback.
          const _BackgroundGlow(),

          // A LayoutBuilder ensures the form can scroll when the keyboard appears.
          LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spacingXl),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Reduced top spacing to pull content higher.
                        const SizedBox(height: DesignTokens.spacingXxl),

                        // Main "Sign Up" title with gradient text.
                        const _GradientTitle(text: 'Sign Up'),
                        // Reduced spacing.
                        const SizedBox(height: DesignTokens.spacingLg),

                        // Google sign-up button.
                        _SocialSignUpButton(
                          iconPath: _kGoogleLogo,
                          text: 'Google',
                          onTap: () {
                            print(
                                'Google Sign Up Tapped'); /* BLoC event call here */
                          },
                        ),
                        // Reduced spacing.
                        const SizedBox(height: DesignTokens.spacingXl),

                        // The main registration form.
                        const _SignUpForm(),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// A specialized widget to render text with a gradient fill.
class _GradientTitle extends StatelessWidget {
  final String text;
  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) =>
          DesignTokens.appNameHeaderGradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Text(
        text,
        style: DesignTokens.appNameStyle.copyWith(fontSize: 50), // Increase as needed
      ),
    );
  }
}

/// A reusable button for social media sign-up options.
class _SocialSignUpButton extends StatelessWidget {
  final String iconPath;
  final String text;
  final VoidCallback onTap;

  const _SocialSignUpButton({
    required this.iconPath,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 46,
        decoration: BoxDecoration(
          color: DesignTokens.googleButtonBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath, height: 24, width: 24),
            const SizedBox(width: DesignTokens.spacingMd),
            Text(text, style: DesignTokens.googleButtonTextStyle),
          ],
        ),
      ),
    );
  }
}

/// The main form widget, containing all the text fields and buttons.
/// It is a StatefulWidget to manage the state of the password visibility.
class _SignUpForm extends StatefulWidget {
  const _SignUpForm();

  @override
  State<_SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<_SignUpForm> {
  bool _isPasswordObscured = true;
  bool _isConfirmPasswordObscured = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First Name and Last Name fields in a row.
        Row(
          children: [
            Expanded(
              child: _TitledTextField(
                title: 'First Name',
                hintText: '',
              ),
            ),
            const SizedBox(width: DesignTokens.spacingMd),
            Expanded(
              child: _TitledTextField(
                title: 'Last Name',
                hintText: '',
              ),
            ),
          ],
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingMd),

        // "What Should Darvis Call You" field.
        _TitledTextField(
          title: 'What Should Darvis Call You',
          hintText: 'e.g., Elon Altman',
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingMd),

        // Email Address field.
        _TitledTextField(
          title: 'Email Address',
          hintText: '<EMAIL>',
          keyboardType: TextInputType.emailAddress,
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingMd),

        // Password field with visibility toggle.
        _TitledTextField(
          title: 'Password',
          hintText: '••••••••',
          isObscured: _isPasswordObscured,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              _isPasswordObscured ? _kEyeOffIcon : _kEyeOnIcon,
              colorFilter: const ColorFilter.mode(
                  DesignTokens.iconSecondary, BlendMode.srcIn),
            ),
            onPressed: () =>
                setState(() => _isPasswordObscured = !_isPasswordObscured),
          ),
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingMd),

        // Confirm Password field with visibility toggle.
        _TitledTextField(
          title: 'Confirm Password',
          hintText: '••••••••',
          isObscured: _isConfirmPasswordObscured,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              _isConfirmPasswordObscured ? _kEyeOffIcon : _kEyeOnIcon,
              colorFilter: const ColorFilter.mode(
                  DesignTokens.iconSecondary, BlendMode.srcIn),
            ),
            onPressed: () => setState(
                () => _isConfirmPasswordObscured = !_isConfirmPasswordObscured),
          ),
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingLg),

        // Link to the login screen.
        _SignInLink(
          onTap: () {
            print('Log In link tapped'); /* BLoC event call here */
          },
        ),
        // Reduced spacing.
        const SizedBox(height: DesignTokens.spacingLg),

        // Main "Sign Up" action button.
        _PrimaryActionButton(
          text: 'Sign Up',
          onTap: () {
            print('Sign Up button tapped'); /* BLoC event call here */
          },
        ),
        const SizedBox(height: DesignTokens.spacingXl),
      ],
    );
  }
}

/// A reusable widget combining a title label and a text input field.
class _TitledTextField extends StatelessWidget {
  final String title;
  final String hintText;
  final bool isObscured;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;

  const _TitledTextField({
    required this.title,
    required this.hintText,
    this.isObscured = false,
    this.suffixIcon,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),
        TextField(
          obscureText: isObscured,
          keyboardType: keyboardType,
          style:
              DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            hintText: hintText,
            hintStyle:
                DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            // Reduced content padding for a more compact field.
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm, // 12px vertical padding
            ),
          ),
        ),
      ],
    );
  }
}

/// A reusable widget for the "Already have an account? Log in" text.
class _SignInLink extends StatelessWidget {
  final VoidCallback onTap;
  const _SignInLink({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Center(
        child: RichText(
        text: TextSpan(
          style: DesignTokens.bodyStyle,
          children: [
            const TextSpan(text: 'Already Have An Account? '),
            TextSpan(
              text: 'Log In.',
              style: DesignTokens.linkTextStyle
                  .copyWith(color: DesignTokens.linkColor),
            ),
          ],
        ),
       ),
      ),
    );
  }
}

/// The main call-to-action button for the screen.
class _PrimaryActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  const _PrimaryActionButton({required this.text, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Center (
    child: GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        // Increased height for more prominence.
        height: 45,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: DesignTokens.primaryButtonBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Text(text, style: DesignTokens.primaryButtonTextStyle),
        ),
      ),
    );
  }
}

/// The decorative background glow effect at the bottom of the screen.
class _BackgroundGlow extends StatelessWidget {
  const _BackgroundGlow();

  @override
  Widget build(BuildContext context) {
    // This Align widget positions the glow at the bottom of its parent.
    return Align(
      alignment: Alignment.bottomCenter,
      // This Container's only job is to hold the shadow. It has no color or child.
      child: Container(
        // We define a large area for the glow to appear in.
        // These can be adjusted based on how far up the screen you want the glow to reach.
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.1,
        decoration: const BoxDecoration(
          // The boxShadow property is what creates the glow.
          boxShadow: [
            BoxShadow(
              // The color of your glow.
              color: Color.fromARGB(255, 9, 38, 107),
              // blurRadius controls the softness. Higher is softer.
              blurRadius: 150.0,
              // spreadRadius makes the glow bigger before blurring.
              spreadRadius: 50.0,
            ),
          ],
        ),
      ),
    );
  }
}
