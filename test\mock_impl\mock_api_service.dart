import '../../lib/services/api_service.dart';

/// Mock implementation of ApiService for testing
class MockApiService implements ApiService {
  final List<Map<String, dynamic>> _tasks = [];
  final List<Map<String, dynamic>> _notes = [];
  final List<Map<String, dynamic>> _conversations = [];
  
  int _nextTaskId = 1;
  int _nextNoteId = 1;
  int _nextConversationId = 1;

  /// Simulate network delay
  Future<void> _simulateDelay() async {
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    await _simulateDelay();
    
    if (email == '<EMAIL>' && password == 'password123') {
      return {
        'access_token': 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
        'refresh_token': 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        'user': {
          'id': 'mock_user_id',
          'email': email,
          'name': 'Test User',
        },
      };
    } else {
      throw Exception('Invalid credentials');
    }
  }

  @override
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    await _simulateDelay();
    
    if (email.contains('@') && password.length >= 6) {
      return {
        'access_token': 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
        'refresh_token': 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        'user': {
          'id': 'mock_user_id_${DateTime.now().millisecondsSinceEpoch}',
          'email': email,
          'name': name,
        },
      };
    } else {
      throw Exception('Invalid registration data');
    }
  }

  @override
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    await _simulateDelay();
    
    return {
      'access_token': 'new_mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
      'refresh_token': 'new_mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
    };
  }

  @override
  Future<Map<String, dynamic>> sendMessage({
    required String message,
    required String conversationId,
  }) async {
    await _simulateDelay();
    
    // Find or create conversation
    var conversation = _conversations.firstWhere(
      (c) => c['id'] == conversationId,
      orElse: () {
        final newConversation = {
          'id': conversationId,
          'messages': <Map<String, dynamic>>[],
          'created_at': DateTime.now().toIso8601String(),
        };
        _conversations.add(newConversation);
        return newConversation;
      },
    );

    // Add user message
    final userMessage = {
      'id': 'msg_${DateTime.now().millisecondsSinceEpoch}',
      'content': message,
      'role': 'user',
      'timestamp': DateTime.now().toIso8601String(),
    };
    conversation['messages'].add(userMessage);

    // Generate mock AI response
    final aiResponse = _generateMockResponse(message);
    final aiMessage = {
      'id': 'msg_${DateTime.now().millisecondsSinceEpoch + 1}',
      'content': aiResponse,
      'role': 'assistant',
      'timestamp': DateTime.now().toIso8601String(),
    };
    conversation['messages'].add(aiMessage);

    return aiMessage;
  }

  @override
  Future<List<Map<String, dynamic>>> getConversations() async {
    await _simulateDelay();
    return List<Map<String, dynamic>>.from(_conversations);
  }

  @override
  Future<Map<String, dynamic>> createTask({
    required String title,
    required String description,
  }) async {
    await _simulateDelay();
    
    final task = {
      'id': 'task_${_nextTaskId++}',
      'title': title,
      'description': description,
      'completed': false,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
    
    _tasks.add(task);
    return task;
  }

  @override
  Future<List<Map<String, dynamic>>> getTasks() async {
    await _simulateDelay();
    return List<Map<String, dynamic>>.from(_tasks);
  }

  @override
  Future<Map<String, dynamic>> updateTask({
    required String taskId,
    Map<String, dynamic>? updates,
  }) async {
    await _simulateDelay();
    
    final taskIndex = _tasks.indexWhere((task) => task['id'] == taskId);
    if (taskIndex == -1) {
      throw Exception('Task not found');
    }
    
    final task = _tasks[taskIndex];
    if (updates != null) {
      task.addAll(updates);
      task['updated_at'] = DateTime.now().toIso8601String();
    }
    
    return task;
  }

  @override
  Future<Map<String, dynamic>> createNote({
    required String title,
    required String content,
  }) async {
    await _simulateDelay();
    
    final note = {
      'id': 'note_${_nextNoteId++}',
      'title': title,
      'content': content,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
    
    _notes.add(note);
    return note;
  }

  @override
  Future<List<Map<String, dynamic>>> getNotes() async {
    await _simulateDelay();
    return List<Map<String, dynamic>>.from(_notes);
  }

  /// Generate a mock AI response based on the input message
  String _generateMockResponse(String message) {
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains('hello') || lowerMessage.contains('hi')) {
      return 'Hello! How can I assist you today?';
    } else if (lowerMessage.contains('task') || lowerMessage.contains('todo')) {
      return 'I can help you manage your tasks. Would you like me to create a new task or show your existing ones?';
    } else if (lowerMessage.contains('note')) {
      return 'I can help you with notes. Would you like to create a new note or review your existing notes?';
    } else if (lowerMessage.contains('weather')) {
      return 'I don\'t have access to real-time weather data in this mock version, but I can help you with other tasks!';
    } else if (lowerMessage.contains('help')) {
      return 'I\'m here to help! I can assist with tasks, notes, conversations, and answer questions. What would you like to do?';
    } else {
      return 'That\'s an interesting question! In this mock version, I can help you with tasks, notes, and general conversation. How can I assist you further?';
    }
  }

  /// Add some sample data for testing
  void addSampleData() {
    // Sample tasks
    _tasks.addAll([
      {
        'id': 'task_1',
        'title': 'Review project proposal',
        'description': 'Go through the new project proposal and provide feedback',
        'completed': false,
        'created_at': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        'updated_at': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
      },
      {
        'id': 'task_2',
        'title': 'Schedule team meeting',
        'description': 'Set up a meeting with the development team for next week',
        'completed': true,
        'created_at': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
        'updated_at': DateTime.now().subtract(const Duration(hours: 3)).toIso8601String(),
      },
    ]);

    // Sample notes
    _notes.addAll([
      {
        'id': 'note_1',
        'title': 'Meeting Notes - Project Kickoff',
        'content': 'Key points from today\'s project kickoff meeting:\n- Timeline: 3 months\n- Team size: 5 developers\n- Budget approved',
        'created_at': DateTime.now().subtract(const Duration(hours: 4)).toIso8601String(),
        'updated_at': DateTime.now().subtract(const Duration(hours: 4)).toIso8601String(),
      },
    ]);

    _nextTaskId = 3;
    _nextNoteId = 2;
  }
}
