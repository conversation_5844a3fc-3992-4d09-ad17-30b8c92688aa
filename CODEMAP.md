# Darvis App - Code Map

> **Auto-generated on:** 2025-08-05  
> **Last updated:** Initial file setup-onboarding screen setup + test for the onboarding screen complete  
> **Status:** Clean Foundation Ready for UI Development

## 📁 Project Structure (Depth 3)

```
├── CODEMAP.md
├── PROGRESS_TRACKER.md
├── README.md
├── analysis_options.yaml
├── android/
│   ├── app/
│   │   ├── build.gradle.kts
│   │   └── src/
│   │       ├── debug/
│   │       ├── main/
│   │       └── profile/
│   ├── build.gradle.kts
│   ├── darvis_app_android.iml
│   ├── gradle/
│   │   └── wrapper/
│   │       ├── gradle-wrapper.jar
│   │       └── gradle-wrapper.properties
│   ├── gradle.properties
│   ├── gradlew
│   ├── gradlew.bat
│   ├── local.properties
│   └── settings.gradle.kts
├── assets/ # Static assets and resources
│   ├── fonts/ # Custom fonts (Outfit, Poppins)
│   │   ├── Outfit-Black.ttf
│   │   ├── Outfit-Bold.ttf
│   │   ├── Outfit-ExtraBold.ttf
│   │   ├── Outfit-ExtraLight.ttf
│   │   ├── Outfit-Light.ttf
│   │   ├── Outfit-Medium.ttf
│   │   ├── Outfit-Regular.ttf
│   │   ├── Outfit-SemiBold.ttf
│   │   ├── Outfit-Thin.ttf
│   │   ├── Poppins-Black.ttf
│   │   ├── Poppins-BlackItalic.ttf
│   │   ├── Poppins-Bold.ttf
│   │   ├── Poppins-BoldItalic.ttf
│   │   ├── Poppins-ExtraBold.ttf
│   │   ├── Poppins-ExtraBoldItalic.ttf
│   │   ├── Poppins-ExtraLight.ttf
│   │   ├── Poppins-ExtraLightItalic.ttf
│   │   ├── Poppins-Italic.ttf
│   │   ├── Poppins-Light.ttf
│   │   ├── Poppins-LightItalic.ttf
│   │   ├── Poppins-Medium.ttf
│   │   ├── Poppins-MediumItalic.ttf
│   │   ├── Poppins-Regular.ttf
│   │   ├── Poppins-SemiBold.ttf
│   │   ├── Poppins-SemiBoldItalic.ttf
│   │   ├── Poppins-Thin.ttf
│   │   └── Poppins-ThinItalic.ttf
│   ├── icons/ # Icon assets (custom icons)
│   │   ├── eye_off.svg
│   │   ├── eye_on.svg
│   │   └── google_logo.svg
│   └── images/ # Image assets (logos, illustrations)
│       ├── darvis_main.PNG
│       ├── onboarding_brain.PNG
│       ├── onboarding_cube.PNG
│       └── onboarding_orb.PNG
├── darvis-app-build.md
├── darvis_app.iml
├── design.json
├── design_tokens.yaml
├── generate_tokens.bat
├── lib/ # Main Flutter application code
│   ├── blocs/ # State management (BLoC pattern)
│   │   ├── auth/ # Authentication state management
│   │   │   ├── auth_bloc.dart
│   │   │   ├── auth_event.dart
│   │   │   └── auth_state.dart
│   │   ├── notes/
│   │   └── tasks/
│   ├── main.dart # App entry point, Firebase init, BLoC providers
│   ├── models/ # Data models and entities
│   ├── screens/ # UI screens and pages
│   │   ├── auth/ # Authentication state management
│   │   │   ├── login_screen.dart
│   │   │   ├── signup_screen.dart
│   │   │   └── welcome_screen.dart
│   │   ├── chat/
│   │   ├── home/
│   │   │   └── home_screen.dart
│   │   ├── onboarding/
│   │   │   └── onboarding_screen.dart
│   │   ├── productivity/
│   │   └── voice/
│   ├── services/ # Business logic and external integrations
│   │   ├── api_service.dart
│   │   ├── auth_service.dart
│   │   ├── livekit_service.dart
│   │   ├── service_locator.dart
│   │   └── sync_engine.dart
│   ├── utils/ # Helper functions and utilities
│   │   └── design_tokens.dart
│   └── widgets/ # Reusable UI components
│       └── common/ # Shared widgets (buttons, cards, inputs)
├── pubspec.lock
├── pubspec.yaml
├── test/ # Testing infrastructure
│   ├── factories/ # Test data generation
│   │   └── mock_data_factory.dart
│   └── mock_impl/ # Mock service implementations for testing
│       ├── mock_api_service.dart
│       └── mock_auth_service.dart
├── tool/ # Development tools and scripts
│   ├── gen_codemap.dart
│   └── gen_design_tokens.dart
├── web/
│   ├── favicon.png
│   ├── icons/ # Icon assets (custom icons)
│   │   ├── Icon-192.png
│   │   ├── Icon-512.png
│   │   ├── Icon-maskable-192.png
│   │   └── Icon-maskable-512.png
│   ├── index.html
│   └── manifest.json
└── windows/
    ├── CMakeLists.txt
    ├── flutter/
    │   ├── CMakeLists.txt
    │   ├── ephemeral/
    │   ├── generated_plugin_registrant.cc
    │   ├── generated_plugin_registrant.h
    │   └── generated_plugins.cmake
    └── runner/
        ├── CMakeLists.txt
        ├── Runner.rc
        ├── flutter_window.cpp
        ├── flutter_window.h
        ├── main.cpp
        ├── resource.h
        ├── resources/
        │   └── app_icon.ico
        ├── runner.exe.manifest
        ├── utils.cpp
        ├── utils.h
        ├── win32_window.cpp
        └── win32_window.h
```

## 📦 Dependencies Overview

| Package | Purpose | Key APIs Used |
|---------|---------|---------------|
| `flutter_bloc` | State management | `BlocProvider`, `BlocBuilder`, `BlocConsumer` |
| `equatable` | Value equality | `Equatable` mixin for BLoC states/events |
| `dio` | HTTP client | `get`, `post`, `interceptors` |
| `dio_smart_retry` | API retry logic | `RetryInterceptor`, exponential backoff |
| `livekit_client` | Real-time voice | `Room`, `LocalAudioTrack`, WebRTC |
| `firebase_core` | Firebase initialization | `Firebase.initializeApp()` |
| `firebase_auth` | Authentication | `signInWithEmailAndPassword`, `authStateChanges` |
| `firebase_messaging` | Push notifications | `onMessage`, `onBackgroundMessage` |
| `firebase_crashlytics` | Crash reporting | `recordError`, `log` |
| `flutter_secure_storage` | Secure token storage | `write`, `read`, `delete` |
| `encrypt` | Data encryption | `AES`, `Key.fromSecureRandom` |
| `get_it` | Dependency injection | `registerLazySingleton`, `registerFactory` |
| `cached_network_image` | Image caching | `CachedNetworkImage` widget |


## 🔄 How the Pieces Fit

**Data Flow Architecture:**
1. **UI Layer** (`screens/`) triggers events → **BLoC Layer** (`blocs/`)
2. **BLoC Layer** calls → **Service Layer** (`services/`) for business logic
3. **Service Layer** communicates with → **External APIs** and **Local Storage**
4. **Design System** (`utils/design_tokens.dart`) provides → **Consistent UI styling**

**Dependency Flow:**
- `main.dart` → initializes `service_locator.dart` → registers all services
- `screens/` → consume BLoCs via `BlocProvider` → trigger state changes
- `blocs/` → depend on `services/` → never call external APIs directly
- `services/` → handle all external communication → Firebase, HTTP, WebRTC
- `utils/` → provide shared utilities → consumed by all layers

**Current State:** Development in progress. Check PROGRESS_TRACKER.md for current status.
