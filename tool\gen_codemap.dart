#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Generates CODEMAP.md with current project structure and dependencies
void main() async {
  print('🗺️  Generating CODEMAP.md...');
  
  final codemap = await generateCodeMap();
  await File('CODEMAP.md').writeAsString(codemap);
  
  print('✅ CODEMAP.md updated successfully!');
}

Future<String> generateCodeMap() async {
  final timestamp = DateTime.now().toIso8601String().split('T')[0];
  final projectStructure = await generateProjectStructure();
  final dependencies = await generateDependenciesTable();
  
  return '''# Darvis App - Code Map

> **Auto-generated on:** $timestamp  
> **Last updated:** ${await getLastCommitMessage()}  
> **Status:** ${await getProjectStatus()}

## 📁 Project Structure (Depth 3)

```
$projectStructure
```

## 📦 Dependencies Overview

$dependencies

## 🔄 How the Pieces Fit

**Data Flow Architecture:**
1. **UI Layer** (`screens/`) triggers events → **BLoC Layer** (`blocs/`)
2. **BLoC Layer** calls → **Service Layer** (`services/`) for business logic
3. **Service Layer** communicates with → **External APIs** and **Local Storage**
4. **Design System** (`utils/design_tokens.dart`) provides → **Consistent UI styling**

**Dependency Flow:**
- `main.dart` → initializes `service_locator.dart` → registers all services
- `screens/` → consume BLoCs via `BlocProvider` → trigger state changes
- `blocs/` → depend on `services/` → never call external APIs directly
- `services/` → handle all external communication → Firebase, HTTP, WebRTC
- `utils/` → provide shared utilities → consumed by all layers

**Current State:** ${await getCurrentPhaseStatus()}
''';
}

Future<String> generateProjectStructure() async {
  final buffer = StringBuffer();
  await _buildTree(Directory('.'), '', 0, 3, buffer);
  return buffer.toString().trim();
}

Future<void> _buildTree(Directory dir, String prefix, int depth, int maxDepth, StringBuffer buffer) async {
  if (depth > maxDepth) return;
  
  final entities = await dir.list().toList();
  entities.sort((a, b) => a.path.compareTo(b.path));
  
  // Filter out unwanted directories
  final filtered = entities.where((entity) {
    final name = entity.path.split(Platform.pathSeparator).last;
    return !['node_modules', '.git', '.dart_tool', 'build', '.idea', '.vscode'].contains(name) &&
           !name.startsWith('.');
  }).toList();
  
  for (int i = 0; i < filtered.length; i++) {
    final entity = filtered[i];
    final isLast = i == filtered.length - 1;
    final name = entity.path.split(Platform.pathSeparator).last;
    
    final connector = isLast ? '└── ' : '├── ';
    final comment = _getDirectoryComment(entity.path, name);
    
    if (entity is Directory) {
      buffer.writeln('$prefix$connector$name/$comment');
      final newPrefix = prefix + (isLast ? '    ' : '│   ');
      await _buildTree(entity, newPrefix, depth + 1, maxDepth, buffer);
    } else {
      buffer.writeln('$prefix$connector$name$comment');
    }
  }
}

String _getDirectoryComment(String path, String name) {
  final comments = {
    'lib': ' # Main Flutter application code',
    'main.dart': ' # App entry point, Firebase init, BLoC providers',
    'blocs': ' # State management (BLoC pattern)',
    'auth': ' # Authentication state management',
    'screens': ' # UI screens and pages',
    'widgets': ' # Reusable UI components',
    'common': ' # Shared widgets (buttons, cards, inputs)',
    'services': ' # Business logic and external integrations',
    'models': ' # Data models and entities',
    'utils': ' # Helper functions and utilities',
    'test': ' # Testing infrastructure',
    'mock_impl': ' # Mock service implementations for testing',
    'factories': ' # Test data generation',
    'assets': ' # Static assets and resources',
    'images': ' # Image assets (logos, illustrations)',
    'icons': ' # Icon assets (custom icons)',
    'fonts': ' # Custom fonts (Outfit, Poppins)',
    'tool': ' # Development tools and scripts',
  };
  
  return comments[name] ?? '';
}

Future<String> generateDependenciesTable() async {
  try {
    final pubspec = await File('pubspec.yaml').readAsString();
    final dependencies = _parseDependencies(pubspec);
    
    final buffer = StringBuffer();
    buffer.writeln('| Package | Purpose | Key APIs Used |');
    buffer.writeln('|---------|---------|---------------|');
    
    for (final dep in dependencies) {
      buffer.writeln('| `${dep['name']}` | ${dep['purpose']} | ${dep['apis']} |');
    }
    
    return buffer.toString();
  } catch (e) {
    return '| Package | Purpose | Key APIs Used |\n|---------|---------|---------------|\n| Error | Could not parse pubspec.yaml | - |';
  }
}

List<Map<String, String>> _parseDependencies(String pubspec) {
  final dependencyPurposes = {
    'flutter_bloc': {'purpose': 'State management', 'apis': '`BlocProvider`, `BlocBuilder`, `BlocConsumer`'},
    'get_it': {'purpose': 'Dependency injection', 'apis': '`registerLazySingleton`, `registerFactory`'},
    'dio': {'purpose': 'HTTP client', 'apis': '`get`, `post`, `interceptors`'},
    'dio_smart_retry': {'purpose': 'API retry logic', 'apis': '`RetryInterceptor`, exponential backoff'},
    'firebase_core': {'purpose': 'Firebase initialization', 'apis': '`Firebase.initializeApp()`'},
    'firebase_auth': {'purpose': 'Authentication', 'apis': '`signInWithEmailAndPassword`, `authStateChanges`'},
    'firebase_messaging': {'purpose': 'Push notifications', 'apis': '`onMessage`, `onBackgroundMessage`'},
    'firebase_crashlytics': {'purpose': 'Crash reporting', 'apis': '`recordError`, `log`'},
    'livekit_client': {'purpose': 'Real-time voice', 'apis': '`Room`, `LocalAudioTrack`, WebRTC'},
    'flutter_secure_storage': {'purpose': 'Secure token storage', 'apis': '`write`, `read`, `delete`'},
    'encrypt': {'purpose': 'Data encryption', 'apis': '`AES`, `Key.fromSecureRandom`'},
    'cached_network_image': {'purpose': 'Image caching', 'apis': '`CachedNetworkImage` widget'},
    'equatable': {'purpose': 'Value equality', 'apis': '`Equatable` mixin for BLoC states/events'},
  };
  
  final dependencies = <Map<String, String>>[];
  final lines = pubspec.split('\n');
  bool inDependencies = false;
  
  for (final line in lines) {
    if (line.trim() == 'dependencies:') {
      inDependencies = true;
      continue;
    }
    if (line.trim() == 'dev_dependencies:') {
      inDependencies = false;
      continue;
    }
    
    if (inDependencies && line.trim().isNotEmpty && line.startsWith('  ') && !line.startsWith('    ')) {
      final packageName = line.trim().split(':')[0];
      if (dependencyPurposes.containsKey(packageName)) {
        dependencies.add({
          'name': packageName,
          'purpose': dependencyPurposes[packageName]!['purpose']!,
          'apis': dependencyPurposes[packageName]!['apis']!,
        });
      }
    }
  }
  
  return dependencies;
}

Future<String> getLastCommitMessage() async {
  try {
    final result = await Process.run('git', ['log', '-1', '--pretty=format:%s']);
    return result.stdout.toString().trim();
  } catch (e) {
    return 'No git history available';
  }
}

Future<String> getProjectStatus() async {
  try {
    final progressTracker = await File('PROGRESS_TRACKER.md').readAsString();
    if (progressTracker.contains('Phase 1 Cleanup: Architecture Reset')) {
      return 'Clean Foundation Ready for UI Development';
    } else if (progressTracker.contains('Phase 0: Project Foundation')) {
      return 'Foundation Complete';
    }
    return 'In Development';
  } catch (e) {
    return 'Status Unknown';
  }
}

Future<String> getCurrentPhaseStatus() async {
  try {
    final progressTracker = await File('PROGRESS_TRACKER.md').readAsString();
    if (progressTracker.contains('Clean foundation with authentication flow')) {
      return 'Clean foundation with authentication flow. Ready for UI-first development approach with mock data before adding persistence and API integration.';
    }
    return 'Development in progress. Check PROGRESS_TRACKER.md for current status.';
  } catch (e) {
    return 'Status unknown. Check PROGRESS_TRACKER.md for details.';
  }
}
