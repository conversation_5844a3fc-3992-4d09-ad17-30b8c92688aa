{"designSystemProfile": {"name": "<PERSON><PERSON> AI Assistant", "version": "1.2.0", "description": "A dark, futuristic design system with blue and green accents, focusing on gradients, glows, and a clean, modern interface. Colors and typography are based on the provided Figma specification.", "colors": {"primary": {"deepBlue": "#111D2F", "accentBlue": "#183C8E", "interactiveBlue": "#004EEB"}, "background": {"app": "#000000", "card": "#111D2F", "accentCard": "#183C8E", "chatBubbleUser": "#111D2F", "chatBubbleBot": "#171717", "textInput": "#DCDFE7", "sideMenu": "#020D1E"}, "text": {"primary": "#FFFFFF", "secondary": "#A9B4C8", "muted": "#7B8499", "onLightBackground": "#222425"}, "icon": {"primary": "#FFFFFF", "secondary": "#A9B4C8"}, "border": {"interactiveElement": "#262626"}}, "gradients": {"appNameHeader": "linear-gradient(180deg, #DCDFE7 0%, #54565D 100%)", "backgroundGlowBlue": "radial-gradient(circle at center, #183C8E 0%, #11272F 80%)", "backgroundGlowGreen": "radial-gradient(circle at center, #183C8E 0%, #03411D 80%)"}, "typography": {"fontFamilies": {"heading": "'Outfit', 'Helvetica Neue', sans-serif", "body": "'Poppins', 'Arial', sans-serif"}, "styles": {"appName": {"fontFamily": "typography.fontFamilies.heading", "fontSize": "36px", "fontWeight": "600", "color": "transparent", "backgroundImage": "gradients.appNameHeader", "backgroundClip": "text"}, "cardTitle": {"fontFamily": "typography.fontFamilies.heading", "fontSize": "18px", "fontWeight": "600", "color": "colors.text.primary"}, "body": {"fontFamily": "typography.fontFamilies.body", "fontSize": "14px", "fontWeight": "400", "color": "colors.text.secondary"}, "chatMessage": {"fontFamily": "typography.fontFamilies.body", "fontSize": "16px", "fontWeight": "400", "color": "colors.text.primary"}}}, "effects": {"glows": {"activeMic": "0 0 15px 5px rgba(24, 60, 142, 0.6)"}}, "assets": {"centralOrbIcon": {"type": "Image", "description": "The main diamond-shaped orb and its metallic border are a pre-rendered image asset and should not be recreated with CSS."}}, "components": {"appBackground": {"context": "Main application background for idle/listening screens.", "style": {"background": "colors.background.app", "backgroundImage": "gradients.backgroundGlowBlue or gradients.backgroundGlowGreen depending on state."}}, "cards": {"context": "Standard content widgets like 'Good Morning' or 'Productivity'.", "style": {"background": "colors.background.card", "borderRadius": "20px"}, "variants": {"accent": {"context": "Action-oriented cards like 'Start Therapy Session'.", "style": {"background": "colors.background.accentCard"}}}}, "buttons": {"microphone": {"context": "The main circular voice input button.", "states": {"default": {"background": "transparent", "border": "1px solid colors.border.interactiveElement", "iconFill": "colors.icon.secondary"}, "active": {"background": "colors.primary.interactiveBlue", "border": "none", "iconFill": "colors.icon.primary", "boxShadow": "effects.glows.activeMic"}}}}, "chatInterface": {"messageBubble": {"variants": {"user": {"context": "Chat bubble for the user's messages.", "style": {"background": "colors.background.chatBubbleUser", "borderRadius": "18px"}}, "bot": {"context": "Chat bubble for the AI's messages.", "style": {"background": "colors.background.chatBubbleBot", "borderRadius": "18px"}}}}, "textInput": {"context": "The text input field at the bottom of the chat screen.", "style": {"background": "colors.background.textInput", "borderRadius": "24px", "color": "colors.text.onLightBackground"}}}}, "notes": {"colorDerivations": "Some colors were not provided and were derived from the visual design for completeness. 'interactiveBlue' (#004EEB) is the solid blue of the active microphone. 'secondary' (#A9B4C8) and 'muted' (#7B8499) text colors were chosen to match the visual hierarchy on dark backgrounds. The 'activeMic' glow is a translucent version of 'accentBlue'.", "assetUsage": "The central diamond/orb element is an image asset, as confirmed. It should be implemented as an `<img>` or SVG, not with CSS gradients.", "textGradients": "The main 'Darvis' app name uses a text gradient. This requires specific CSS properties (`background-image`, `background-clip: text`, `color: transparent`)."}}}