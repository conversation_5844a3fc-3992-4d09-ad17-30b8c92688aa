import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';

import '../../lib/services/auth_service.dart';

/// Mock implementation of AuthService for testing
class MockAuthService implements AuthService {
  bool _isAuthenticated = false;
  User? _currentUser;
  String? _accessToken;
  String? _refreshToken;
  
  final StreamController<User?> _authStateController = StreamController<User?>.broadcast();

  @override
  Stream<User?> get authStateChanges => _authStateController.stream;

  @override
  User? get currentUser => _currentUser;

  @override
  bool get isAuthenticated => _isAuthenticated;

  /// Set mock authentication state
  void setAuthenticationState({
    required bool isAuthenticated,
    User? user,
    String? accessToken,
    String? refreshToken,
  }) {
    _isAuthenticated = isAuthenticated;
    _currentUser = user;
    _accessToken = accessToken;
    _refreshToken = refreshToken;
    _authStateController.add(user);
  }

  @override
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Mock validation
    if (email == '<EMAIL>' && password == 'password123') {
      final mockUser = MockUser(
        uid: 'mock_user_id',
        email: email,
        displayName: 'Test User',
      );
      
      setAuthenticationState(
        isAuthenticated: true,
        user: mockUser,
        accessToken: 'mock_access_token',
        refreshToken: 'mock_refresh_token',
      );
      
      return MockUserCredential(user: mockUser);
    } else {
      throw Exception('Invalid email or password');
    }
  }

  @override
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Mock validation
    if (email.contains('@') && password.length >= 6) {
      final mockUser = MockUser(
        uid: 'mock_user_id_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        displayName: name,
      );
      
      setAuthenticationState(
        isAuthenticated: true,
        user: mockUser,
        accessToken: 'mock_access_token',
        refreshToken: 'mock_refresh_token',
      );
      
      return MockUserCredential(user: mockUser);
    } else {
      throw Exception('Invalid registration data');
    }
  }

  @override
  Future<void> signOut() async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    setAuthenticationState(
      isAuthenticated: false,
      user: null,
      accessToken: null,
      refreshToken: null,
    );
  }

  @override
  Future<String?> getAccessToken() async {
    return _accessToken;
  }

  @override
  Future<String?> getRefreshToken() async {
    return _refreshToken;
  }

  @override
  Future<String?> refreshAccessToken() async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    if (_refreshToken != null) {
      _accessToken = 'new_mock_access_token_${DateTime.now().millisecondsSinceEpoch}';
      return _accessToken;
    } else {
      throw Exception('No refresh token available');
    }
  }

  /// Dispose of resources
  void dispose() {
    _authStateController.close();
  }
}

/// Mock User implementation
class MockUser implements User {
  @override
  final String uid;
  
  @override
  final String? email;
  
  @override
  final String? displayName;

  MockUser({
    required this.uid,
    this.email,
    this.displayName,
  });

  @override
  Future<void> updateDisplayName(String? displayName) async {
    // Mock implementation
  }

  @override
  Future<void> delete() async {
    // Mock implementation
  }

  // Implement other User properties with default/mock values
  @override
  bool get emailVerified => true;

  @override
  bool get isAnonymous => false;

  @override
  UserMetadata get metadata => MockUserMetadata();

  @override
  String? get phoneNumber => null;

  @override
  String? get photoURL => null;

  @override
  List<UserInfo> get providerData => [];

  @override
  String? get refreshToken => 'mock_refresh_token';

  @override
  String? get tenantId => null;

  @override
  Future<void> reload() async {}

  @override
  Future<String> getIdToken([bool forceRefresh = false]) async {
    return 'mock_id_token';
  }

  @override
  Future<IdTokenResult> getIdTokenResult([bool forceRefresh = false]) async {
    return MockIdTokenResult();
  }

  @override
  Future<void> updateEmail(String newEmail) async {}

  @override
  Future<void> updatePassword(String newPassword) async {}

  @override
  Future<void> updatePhoneNumber(PhoneAuthCredential phoneCredential) async {}

  @override
  Future<void> updatePhotoURL(String? photoURL) async {}

  @override
  Future<void> updateProfile({String? displayName, String? photoURL}) async {}

  @override
  Future<void> sendEmailVerification([ActionCodeSettings? actionCodeSettings]) async {}

  @override
  Future<void> verifyBeforeUpdateEmail(String newEmail, [ActionCodeSettings? actionCodeSettings]) async {}

  @override
  Future<UserCredential> linkWithCredential(AuthCredential credential) async {
    return MockUserCredential(user: this);
  }

  @override
  Future<UserCredential> reauthenticateWithCredential(AuthCredential credential) async {
    return MockUserCredential(user: this);
  }

  @override
  Future<User> unlink(String providerId) async {
    return this;
  }

  @override
  MultiFactor get multiFactor => MockMultiFactor();
}

/// Mock UserCredential implementation
class MockUserCredential implements UserCredential {
  @override
  final User? user;

  MockUserCredential({this.user});

  @override
  AdditionalUserInfo? get additionalUserInfo => null;

  @override
  AuthCredential? get credential => null;
}

/// Mock UserMetadata implementation
class MockUserMetadata implements UserMetadata {
  @override
  DateTime? get creationTime => DateTime.now();

  @override
  DateTime? get lastSignInTime => DateTime.now();
}

/// Mock IdTokenResult implementation
class MockIdTokenResult implements IdTokenResult {
  @override
  Map<String, dynamic> get claims => {};

  @override
  DateTime? get expirationTime => DateTime.now().add(const Duration(hours: 1));

  @override
  DateTime? get issuedAtTime => DateTime.now();

  @override
  String? get signInProvider => 'password';

  @override
  String? get signInSecondFactor => null;

  @override
  String? get token => 'mock_id_token';

  @override
  DateTime? get authTime => DateTime.now();
}

/// Mock MultiFactor implementation
class MockMultiFactor implements MultiFactor {
  @override
  List<MultiFactorInfo> get enrolledFactors => [];

  @override
  Future<MultiFactorSession> getSession() async {
    throw UnimplementedError();
  }

  @override
  Future<void> enroll(MultiFactorAssertion assertion, MultiFactorSession session) async {}

  @override
  Future<void> unenroll({String? factorUid, MultiFactorInfo? multiFactorInfo}) async {}
}
