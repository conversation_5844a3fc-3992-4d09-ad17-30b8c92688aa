import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'api_service.dart';

/// Service for handling authentication with Firebase and backend API
class AuthService {
  final ApiService _apiService;
  final FlutterSecureStorage _secureStorage;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';

  AuthService({
    required ApiService apiService,
    required FlutterSecureStorage secureStorage,
  })  : _apiService = apiService,
        _secureStorage = secureStorage;

  /// Stream of authentication state changes
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  /// Get current user
  User? get currentUser => _firebaseAuth.currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  /// Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Get backend tokens
      await _authenticateWithBackend(email, password);

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleFirebaseAuthException(e);
    }
  }

  /// Create account with email and password
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name
      await credential.user?.updateDisplayName(name);

      // Register with backend
      await _registerWithBackend(email, password, name);

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleFirebaseAuthException(e);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
    await _clearTokens();
  }

  /// Get stored access token
  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: _accessTokenKey);
  }

  /// Get stored refresh token
  Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: _refreshTokenKey);
  }

  /// Refresh access token
  Future<String?> refreshAccessToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        throw Exception('No refresh token available');
      }

      final response = await _apiService.refreshToken(refreshToken);
      final newAccessToken = response['access_token'] as String?;
      final newRefreshToken = response['refresh_token'] as String?;

      if (newAccessToken != null) {
        await _secureStorage.write(key: _accessTokenKey, value: newAccessToken);
      }
      if (newRefreshToken != null) {
        await _secureStorage.write(key: _refreshTokenKey, value: newRefreshToken);
      }

      return newAccessToken;
    } catch (e) {
      // If refresh fails, sign out user
      await signOut();
      rethrow;
    }
  }

  /// Authenticate with backend API
  Future<void> _authenticateWithBackend(String email, String password) async {
    try {
      final response = await _apiService.login(email: email, password: password);
      await _storeTokens(response);
    } catch (e) {
      // If backend auth fails, sign out from Firebase
      await _firebaseAuth.signOut();
      rethrow;
    }
  }

  /// Register with backend API
  Future<void> _registerWithBackend(String email, String password, String name) async {
    try {
      final response = await _apiService.register(
        email: email,
        password: password,
        name: name,
      );
      await _storeTokens(response);
    } catch (e) {
      // If backend registration fails, delete Firebase user
      await currentUser?.delete();
      rethrow;
    }
  }

  /// Store tokens securely
  Future<void> _storeTokens(Map<String, dynamic> response) async {
    final accessToken = response['access_token'] as String?;
    final refreshToken = response['refresh_token'] as String?;

    if (accessToken != null) {
      await _secureStorage.write(key: _accessTokenKey, value: accessToken);
    }
    if (refreshToken != null) {
      await _secureStorage.write(key: _refreshTokenKey, value: refreshToken);
    }
  }

  /// Clear stored tokens
  Future<void> _clearTokens() async {
    await _secureStorage.delete(key: _accessTokenKey);
    await _secureStorage.delete(key: _refreshTokenKey);
  }

  /// Handle Firebase Auth exceptions
  Exception _handleFirebaseAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return Exception('No user found with this email address.');
      case 'wrong-password':
        return Exception('Incorrect password.');
      case 'email-already-in-use':
        return Exception('An account already exists with this email address.');
      case 'weak-password':
        return Exception('Password is too weak.');
      case 'invalid-email':
        return Exception('Invalid email address.');
      case 'user-disabled':
        return Exception('This account has been disabled.');
      case 'too-many-requests':
        return Exception('Too many failed attempts. Please try again later.');
      default:
        return Exception('Authentication failed: ${e.message}');
    }
  }
}
