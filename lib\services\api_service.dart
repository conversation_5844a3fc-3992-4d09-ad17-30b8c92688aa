import 'package:dio/dio.dart';

/// Service for handling all HTTP API communication with the Darvis backend
class ApiService {
  final Dio _dio;

  ApiService(this._dio);

  // Authentication endpoints
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _dio.post('/auth/login', data: {
        'email': email,
        'password': password,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      final response = await _dio.post('/auth/register', data: {
        'email': email,
        'password': password,
        'name': name,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      final response = await _dio.post('/auth/refresh', data: {
        'refresh_token': refreshToken,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Chat endpoints
  Future<Map<String, dynamic>> sendMessage({
    required String message,
    required String conversationId,
  }) async {
    try {
      final response = await _dio.post('/chat/message', data: {
        'message': message,
        'conversation_id': conversationId,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<List<Map<String, dynamic>>> getConversations() async {
    try {
      final response = await _dio.get('/chat/conversations');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Tasks endpoints
  Future<Map<String, dynamic>> createTask({
    required String title,
    required String description,
  }) async {
    try {
      final response = await _dio.post('/tasks', data: {
        'title': title,
        'description': description,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<List<Map<String, dynamic>>> getTasks() async {
    try {
      final response = await _dio.get('/tasks');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> updateTask({
    required String taskId,
    Map<String, dynamic>? updates,
  }) async {
    try {
      final response = await _dio.patch('/tasks/$taskId', data: updates);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Notes endpoints
  Future<Map<String, dynamic>> createNote({
    required String title,
    required String content,
  }) async {
    try {
      final response = await _dio.post('/notes', data: {
        'title': title,
        'content': content,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<List<Map<String, dynamic>>> getNotes() async {
    try {
      final response = await _dio.get('/notes');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Error handling
  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('Connection timeout. Please check your internet connection.');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Unknown error occurred';
        return Exception('Server error ($statusCode): $message');
      case DioExceptionType.cancel:
        return Exception('Request was cancelled');
      case DioExceptionType.connectionError:
        return Exception('No internet connection');
      default:
        return Exception('Unexpected error: ${e.message}');
    }
  }
}
